/* Footer Pages Styles */

/* Contact Page */
.contact-section {
    padding: 3rem 0;
    background: var(--neutral-50);
}

.contact-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-form-container {
    background: white;
    padding: 3rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.contact-form-container h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--neutral-700);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: var(--transition-fast);
    outline: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--neutral-600);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-label .checkmark {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: 0.25rem;
    position: relative;
    transition: var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

/* Contact Information */
.contact-info {
    background: white;
    padding: 3rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
}

.contact-info h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--neutral-100);
}

.contact-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.contact-icon {
    width: 3rem;
    height: 3rem;
    background: var(--primary-50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-500);
    font-size: 1.25rem;
    flex-shrink: 0;
}

.contact-details h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--neutral-600);
    line-height: 1.6;
}

.contact-details a {
    color: var(--primary-600);
    text-decoration: none;
    transition: var(--transition-fast);
}

.contact-details a:hover {
    color: var(--primary-700);
    text-decoration: underline;
}

/* Social Section */
.social-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--neutral-100);
}

.social-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1rem;
}

.social-section .social-links {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.social-section .social-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--neutral-50);
    border-radius: 0.5rem;
    color: var(--neutral-700);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-section .social-link:hover {
    background: var(--neutral-100);
    transform: translateX(4px);
}

.social-section .social-link.instagram:hover {
    background: #e1306c;
    color: white;
}

.social-section .social-link.twitter:hover {
    background: #1da1f2;
    color: white;
}

.social-section .social-link.facebook:hover {
    background: #4267b2;
    color: white;
}

.social-section .social-link.tiktok:hover {
    background: #000000;
    color: white;
}

/* Quick Help Section */
.quick-help {
    padding: 3rem 0;
    background: white;
}

.quick-help h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 3rem;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.help-item {
    background: var(--neutral-50);
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    text-decoration: none;
    color: var(--neutral-700);
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.help-item:hover {
    background: white;
    border-color: var(--primary-200);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.help-item i {
    font-size: 2.5rem;
    color: var(--primary-500);
    margin-bottom: 1rem;
}

.help-item h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
}

.help-item p {
    font-size: 0.875rem;
    color: var(--neutral-600);
}

/* FAQ Page */
.faq-section {
    padding: 3rem 0;
    background: var(--neutral-50);
}

.faq-search {
    max-width: 600px;
    margin: 0 auto 3rem;
    position: relative;
}

.faq-search input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.75rem;
    font-size: 1.125rem;
    outline: none;
    transition: var(--transition-fast);
}

.faq-search input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.faq-search i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--neutral-400);
}

.faq-categories {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.faq-category {
    padding: 0.75rem 1.5rem;
    background: white;
    border: 2px solid var(--neutral-200);
    border-radius: 2rem;
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.faq-category:hover,
.faq-category.active {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
}

.faq-list {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border-radius: 0.75rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.faq-question {
    width: 100%;
    padding: 1.5rem;
    background: none;
    border: none;
    text-align: left;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition-fast);
}

.faq-question:hover {
    background: var(--neutral-50);
}

.faq-question i {
    color: var(--primary-500);
    transition: var(--transition-fast);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem;
    max-height: 500px;
}

.faq-answer p {
    color: var(--neutral-600);
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .contact-form-container,
    .contact-info {
        padding: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .help-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .help-item {
        padding: 1.5rem;
    }
    
    .faq-categories {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }
    
    .faq-category {
        white-space: nowrap;
        flex-shrink: 0;
    }
}

@media (max-width: 480px) {
    .contact-form-container,
    .contact-info {
        padding: 1.5rem;
    }
    
    .contact-item {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-icon {
        align-self: center;
    }
    
    .faq-question {
        padding: 1rem;
        font-size: 1rem;
    }
    
    .faq-answer {
        padding: 0 1rem;
    }
    
    .faq-item.active .faq-answer {
        padding: 0 1rem 1rem;
    }
}
