/* Cart Page Styles */

.nav-icon.active {
    color: var(--primary-500);
}

/* Cart Section */
.cart-section {
    padding: 3rem 0;
    background: var(--neutral-50);
    min-height: 60vh;
}

.cart-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

/* Cart Items */
.cart-items {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--neutral-200);
}

.cart-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neutral-900);
}

.clear-cart-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: transparent;
    border: 1px solid var(--accent-300);
    border-radius: 0.5rem;
    color: var(--accent-600);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.clear-cart-btn:hover {
    background: var(--accent-50);
    border-color: var(--accent-400);
}

/* Cart Item */
.cart-item {
    display: grid;
    grid-template-columns: 100px 1fr auto auto auto;
    gap: 1rem;
    align-items: center;
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--neutral-100);
}

.cart-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 100px;
    height: 100px;
    border-radius: 0.5rem;
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.25rem;
}

.item-details p {
    color: var(--neutral-600);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.item-price {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
}

.item-price .original-price {
    font-size: 0.875rem;
    color: var(--neutral-400);
    text-decoration: line-through;
    margin-left: 0.5rem;
}

/* Quantity Controls */
.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    overflow: hidden;
}

.quantity-btn {
    width: 2.5rem;
    height: 2.5rem;
    border: none;
    background: var(--neutral-100);
    color: var(--neutral-700);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    background: var(--neutral-200);
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-input {
    width: 3rem;
    height: 2.5rem;
    border: none;
    text-align: center;
    font-weight: 600;
    outline: none;
    background: white;
}

.remove-btn {
    padding: 0.5rem;
    background: transparent;
    border: none;
    color: var(--accent-500);
    cursor: pointer;
    transition: var(--transition-fast);
    border-radius: 0.25rem;
}

.remove-btn:hover {
    background: var(--accent-50);
    color: var(--accent-600);
}

/* Continue Shopping */
.continue-shopping {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--neutral-200);
}

/* Cart Summary */
.cart-summary {
    position: sticky;
    top: 6rem;
}

.summary-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.summary-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1.5rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--neutral-700);
}

.summary-row.total {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1.5rem;
}

.summary-divider {
    height: 1px;
    background: var(--neutral-200);
    margin: 1.5rem 0;
}

/* Promo Code */
.promo-section {
    margin: 1.5rem 0;
}

.promo-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.promo-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    outline: none;
    transition: var(--transition-fast);
}

.promo-input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.promo-btn {
    padding: 0.75rem 1rem;
    background: var(--neutral-900);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.promo-btn:hover {
    background: var(--neutral-800);
}

.promo-message {
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.promo-message.success {
    color: var(--primary-600);
}

.promo-message.error {
    color: var(--accent-600);
}

/* Checkout Button */
.checkout-btn {
    width: 100%;
    margin: 1.5rem 0;
    padding: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

/* Payment Methods */
.payment-methods {
    text-align: center;
    margin: 1.5rem 0;
}

.payment-methods span {
    display: block;
    font-size: 0.875rem;
    color: var(--neutral-600);
    margin-bottom: 0.5rem;
}

.payment-icons {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    color: var(--neutral-400);
}

/* Security Badge */
.security-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--primary-50);
    border-radius: 0.5rem;
    color: var(--primary-700);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Recommended Products */
.recommended-section {
    padding: 3rem 0;
    background: white;
}

.recommended-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

/* Empty Cart */
.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-cart-icon {
    font-size: 4rem;
    color: var(--neutral-300);
    margin-bottom: 1rem;
}

.empty-cart h3 {
    font-size: 1.5rem;
    color: var(--neutral-700);
    margin-bottom: 0.5rem;
}

.empty-cart p {
    color: var(--neutral-500);
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cart-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .cart-item {
        grid-template-columns: 80px 1fr;
        gap: 1rem;
    }
    
    .item-actions {
        grid-column: 1 / -1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
    }
    
    .cart-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .summary-card {
        padding: 1.5rem;
    }
    
    .recommended-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .cart-items,
    .summary-card {
        padding: 1rem;
    }
    
    .cart-item {
        grid-template-columns: 60px 1fr;
        gap: 0.75rem;
    }
    
    .item-image {
        width: 60px;
        height: 60px;
    }
    
    .promo-input-group {
        flex-direction: column;
    }
    
    .payment-icons {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
}
