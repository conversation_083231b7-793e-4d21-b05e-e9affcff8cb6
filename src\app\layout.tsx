import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Magna5RRR - Premium Streetwear & Lifestyle',
  description: 'Discover unique streetwear, perfumes, jewelry, and essentials at Magna5RRR. Where style meets authenticity.',
  keywords: 'streetwear, fashion, perfumes, jewelry, lifestyle, clothing, accessories',
  authors: [{ name: 'Magna5RRR' }],
  creator: 'Magna5RRR',
  publisher: 'Magna5RRR',
  openGraph: {
    title: 'Magna5RRR - Premium Streetwear & Lifestyle',
    description: 'Discover unique streetwear, perfumes, jewelry, and essentials at Magna5RRR. Where style meets authenticity.',
    url: 'https://magna5rrr.com',
    siteName: 'Magna5RRR',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Magna5RRR Brand',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Magna5RRR - Premium Streetwear & Lifestyle',
    description: 'Discover unique streetwear, perfumes, jewelry, and essentials at Magna5RRR. Where style meets authenticity.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100">
          {children}
        </div>
      </body>
    </html>
  )
}
