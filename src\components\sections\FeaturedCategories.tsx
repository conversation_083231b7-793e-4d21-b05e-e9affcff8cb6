'use client'

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ArrowRight } from 'lucide-react'

const categories = [
  {
    id: 1,
    name: 'Streetwear',
    description: 'Bold designs that make a statement',
    image: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    href: '/clothing',
    color: 'from-neutral-900 to-neutral-700'
  },
  {
    id: 2,
    name: 'Perfumes',
    description: 'Signature scents for every mood',
    image: 'https://images.unsplash.com/photo-1541643600914-78b084683601?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    href: '/perfumes',
    color: 'from-primary-600 to-primary-800'
  },
  {
    id: 3,
    name: 'Jewelry',
    description: 'Elegant pieces that elevate your style',
    image: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    href: '/jewelry',
    color: 'from-accent-500 to-accent-700'
  },
  {
    id: 4,
    name: 'Essentials',
    description: 'Daily must-haves for modern living',
    image: 'https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    href: '/essentials',
    color: 'from-neutral-600 to-neutral-800'
  }
]

const FeaturedCategories = () => {
  return (
    <section className="py-20 bg-neutral-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-display font-bold text-neutral-900 mb-4">
            Explore Our Universe
          </h2>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
            From cutting-edge streetwear to luxury fragrances, discover collections that define your unique style
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {categories.map((category, index) => (
            <Link
              key={category.id}
              href={category.href}
              className="group relative overflow-hidden rounded-2xl aspect-[3/4] transform transition-all duration-500 hover:scale-105 hover:shadow-2xl"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Background Image */}
              <Image
                src={category.image}
                alt={category.name}
                fill
                className="object-cover transition-transform duration-700 group-hover:scale-110"
              />
              
              {/* Overlay */}
              <div className={`absolute inset-0 bg-gradient-to-t ${category.color} opacity-60 group-hover:opacity-70 transition-opacity duration-300`}></div>
              
              {/* Content */}
              <div className="absolute inset-0 p-6 flex flex-col justify-end text-white">
                <h3 className="text-2xl font-bold mb-2 transform transition-transform duration-300 group-hover:translate-y-[-4px]">
                  {category.name}
                </h3>
                <p className="text-neutral-200 mb-4 transform transition-all duration-300 group-hover:translate-y-[-4px] group-hover:text-white">
                  {category.description}
                </p>
                <div className="flex items-center text-white font-medium transform transition-all duration-300 group-hover:translate-y-[-4px]">
                  Shop Now
                  <ArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-2" />
                </div>
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Link>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <Link
            href="/collections"
            className="inline-flex items-center px-8 py-4 bg-neutral-900 text-white font-medium rounded-lg hover:bg-neutral-800 transition-colors duration-200 group"
          >
            View All Collections
            <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
          </Link>
        </div>
      </div>
    </section>
  )
}

export default FeaturedCategories
