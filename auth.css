/* Authentication Styles */

.account-section,
.auth-section {
    padding: 6rem 0 3rem;
    background: var(--neutral-50);
    min-height: 80vh;
}

/* Auth Container */
.auth-container {
    max-width: 500px;
    margin: 0 auto;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-tabs {
    display: flex;
    background: var(--neutral-100);
}

.auth-tab {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    color: var(--neutral-600);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.auth-tab.active {
    background: white;
    color: var(--neutral-900);
}

.auth-form-container {
    padding: 2rem;
}

.auth-title {
    font-family: var(--font-display);
    font-size: 2rem;
    font-weight: bold;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
    text-align: center;
}

.auth-subtitle {
    color: var(--neutral-600);
    text-align: center;
    margin-bottom: 2rem;
}

/* Form Styles */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--neutral-700);
}

.form-group input {
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: var(--transition-fast);
    outline: none;
}

.form-group input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--neutral-400);
    cursor: pointer;
    padding: 0.25rem;
}

.password-toggle:hover {
    color: var(--neutral-600);
}

.password-strength {
    height: 4px;
    background: var(--neutral-200);
    border-radius: 2px;
    margin-top: 0.5rem;
    overflow: hidden;
}

.password-strength::after {
    content: '';
    display: block;
    height: 100%;
    width: 0%;
    background: var(--accent-500);
    transition: var(--transition-normal);
}

.password-strength.weak::after {
    width: 33%;
    background: var(--accent-500);
}

.password-strength.medium::after {
    width: 66%;
    background: #f59e0b;
}

.password-strength.strong::after {
    width: 100%;
    background: #10b981;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    color: var(--neutral-700);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-label .checkmark {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: 0.25rem;
    position: relative;
    transition: var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-500);
    text-decoration: none;
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary-600);
    text-decoration: underline;
}

.auth-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
    margin-top: 1rem;
}

/* Social Auth */
.social-auth {
    margin-top: 2rem;
    text-align: center;
}

.social-auth p {
    color: var(--neutral-500);
    margin-bottom: 1rem;
    position: relative;
}

.social-auth p::before,
.social-auth p::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 30%;
    height: 1px;
    background: var(--neutral-300);
}

.social-auth p::before {
    left: 0;
}

.social-auth p::after {
    right: 0;
}

.social-buttons {
    display: flex;
    gap: 1rem;
}

.social-btn {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    background: white;
    color: var(--neutral-700);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.google-btn:hover {
    border-color: #db4437;
    color: #db4437;
}

.facebook-btn:hover {
    border-color: #4267b2;
    color: #4267b2;
}

/* Dashboard */
.dashboard-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-sidebar {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
    position: sticky;
    top: 6rem;
}

.user-profile {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--neutral-200);
}

.user-avatar {
    width: 4rem;
    height: 4rem;
    background: var(--primary-500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.user-info h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.25rem;
}

.user-info p {
    color: var(--neutral-600);
    font-size: 0.875rem;
}

.dashboard-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dashboard-nav .nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    color: var(--neutral-700);
    text-decoration: none;
    transition: var(--transition-fast);
}

.dashboard-nav .nav-item:hover {
    background: var(--neutral-100);
    color: var(--neutral-900);
}

.dashboard-nav .nav-item.active {
    background: var(--primary-500);
    color: white;
}

.dashboard-content {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.dashboard-section h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 2rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--neutral-50);
    border-radius: 0.75rem;
    border: 1px solid var(--neutral-200);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    background: var(--primary-500);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-info h3 {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--neutral-900);
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: var(--neutral-600);
    font-size: 0.875rem;
}

/* Recent Orders */
.recent-orders h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1rem;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--neutral-200);
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
}

.order-info h4 {
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.25rem;
}

.order-info p {
    color: var(--neutral-600);
    font-size: 0.875rem;
}

.order-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.order-status.delivered {
    background: #dcfce7;
    color: #166534;
}

.order-status.processing {
    background: #fef3c7;
    color: #92400e;
}

.order-status.shipped {
    background: #dbeafe;
    color: #1e40af;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .dashboard-sidebar {
        position: static;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .social-buttons {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .auth-container {
        margin: 0 1rem;
    }
    
    .auth-form-container {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .account-section {
        padding: 5rem 0 2rem;
    }
    
    .auth-form-container {
        padding: 1rem;
    }
    
    .dashboard-content {
        padding: 1.5rem;
    }
    
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
