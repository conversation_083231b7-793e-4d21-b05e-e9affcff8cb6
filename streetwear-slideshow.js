// Dynamic Streetwear Art Slideshow
class StreetwareSlideshow {
    constructor() {
        this.currentSlide = 0;
        this.isPlaying = true;
        this.slideInterval = null;
        this.slideDuration = 5000; // 5 seconds per slide
        
        this.slides = [
            {
                id: 1,
                type: 'streetwear',
                title: 'LUXURY STREETWEAR',
                subtitle: '<PERSON> × Corteiz × Prada',
                description: 'The intersection of high fashion and street culture. Where luxury meets rebellion.',
                image: 'https://images.unsplash.com/photo-1556821840-3a9c6dcb0e78?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                overlay: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'SHOP HEAT',
                link: 'clothing.html',
                mood: 'energetic'
            },
            {
                id: 2,
                type: 'art',
                title: 'GALLERY TO STREET',
                subtitle: 'Wearable Art Pieces',
                description: 'Underground artists collaborate with fashion houses to create pieces that blur the line between gallery and wardrobe.',
                image: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80',
                overlay: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'VIEW COLLECTION',
                link: 'collections.html',
                mood: 'mysterious'
            },
            {
                id: 3,
                type: 'lifestyle',
                title: 'URBAN ESSENCE',
                subtitle: 'Fragrances for the Streets',
                description: 'Scents that capture the raw energy of city life, rebellion, and artistic expression.',
                image: 'https://images.unsplash.com/photo-1594035910387-fea47794261f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                overlay: 'https://images.unsplash.com/photo-1541643600914-78b084683601?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'DISCOVER SCENTS',
                link: 'perfumes.html',
                mood: 'calm'
            },
            {
                id: 4,
                type: 'accessories',
                title: 'STATEMENT JEWELRY',
                subtitle: 'Pieces That Command Attention',
                description: 'Bold accessories that complete your look and express your uncompromising individuality.',
                image: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                overlay: 'https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'SHOP JEWELRY',
                link: 'jewelry.html',
                mood: 'energetic'
            },
            {
                id: 5,
                type: 'culture',
                title: 'STREET ESSENTIALS',
                subtitle: 'Tools for Urban Warriors',
                description: 'Functional gear designed for those who live and breathe street culture.',
                image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                overlay: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'GET ESSENTIALS',
                link: 'essentials.html',
                mood: 'calm'
            }
        ];
        
        this.init();
    }

    init() {
        this.createSlideshowHTML();
        this.setupEventListeners();
        this.startSlideshow();
        this.preloadImages();
    }

    createSlideshowHTML() {
        const heroSection = document.querySelector('.hero');
        if (!heroSection) return;

        heroSection.innerHTML = `
            <div class="streetwear-slideshow" id="streetwareSlideshow">
                <div class="slideshow-container">
                    <div class="slides-wrapper" id="slidesWrapper">
                        ${this.slides.map((slide, index) => this.createSlideHTML(slide, index)).join('')}
                    </div>
                    
                    <div class="slideshow-overlay">
                        <div class="container">
                            <div class="slide-content" id="slideContent">
                                <div class="slide-category" id="slideCategory">${this.slides[0].type.toUpperCase()}</div>
                                <h1 class="slide-title" id="slideTitle">${this.slides[0].title}</h1>
                                <p class="slide-subtitle" id="slideSubtitle">${this.slides[0].subtitle}</p>
                                <p class="slide-description" id="slideDescription">${this.slides[0].description}</p>
                                <div class="slide-cta">
                                    <a href="${this.slides[0].link}" class="btn-street" id="slideCTA">${this.slides[0].cta}</a>
                                    <button class="btn-street-outline" onclick="streetwareSlideshow.pausePlay()">
                                        <span id="pausePlayText">PAUSE</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="slideshow-controls">
                        <button class="slide-nav prev" onclick="streetwareSlideshow.previousSlide()">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="slide-nav next" onclick="streetwareSlideshow.nextSlide()">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    
                    <div class="slide-indicators" id="slideIndicators">
                        ${this.slides.map((_, index) => `
                            <button class="indicator ${index === 0 ? 'active' : ''}" 
                                    onclick="streetwareSlideshow.goToSlide(${index})">
                                <span class="indicator-progress"></span>
                            </button>
                        `).join('')}
                    </div>
                    
                    <div class="slide-progress" id="slideProgress">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>
                </div>
            </div>
        `;

        this.addSlideshowStyles();
    }

    createSlideHTML(slide, index) {
        return `
            <div class="slide ${index === 0 ? 'active' : ''}" data-slide="${index}" data-mood="${slide.mood}">
                <div class="slide-background">
                    <img src="${slide.image}" alt="${slide.title}" class="slide-image">
                    <div class="slide-gradient"></div>
                </div>
                <div class="slide-art-overlay">
                    <img src="${slide.overlay}" alt="Art overlay" class="art-overlay">
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                    this.previousSlide();
                    break;
                case 'ArrowRight':
                    this.nextSlide();
                    break;
                case ' ':
                    e.preventDefault();
                    this.pausePlay();
                    break;
            }
        });

        // Touch/swipe support
        let startX = 0;
        let endX = 0;
        
        const slideshow = document.getElementById('streetwareSlideshow');
        if (slideshow) {
            slideshow.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
            });

            slideshow.addEventListener('touchend', (e) => {
                endX = e.changedTouches[0].clientX;
                const diff = startX - endX;
                
                if (Math.abs(diff) > 50) { // Minimum swipe distance
                    if (diff > 0) {
                        this.nextSlide();
                    } else {
                        this.previousSlide();
                    }
                }
            });
        }

        // Pause on hover
        if (slideshow) {
            slideshow.addEventListener('mouseenter', () => {
                this.pauseSlideshow();
            });

            slideshow.addEventListener('mouseleave', () => {
                if (this.isPlaying) {
                    this.startSlideshow();
                }
            });
        }
    }

    startSlideshow() {
        this.slideInterval = setInterval(() => {
            this.nextSlide();
        }, this.slideDuration);
        
        this.startProgressBar();
    }

    pauseSlideshow() {
        if (this.slideInterval) {
            clearInterval(this.slideInterval);
            this.slideInterval = null;
        }
        this.pauseProgressBar();
    }

    nextSlide() {
        this.currentSlide = (this.currentSlide + 1) % this.slides.length;
        this.updateSlide();
    }

    previousSlide() {
        this.currentSlide = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1;
        this.updateSlide();
    }

    goToSlide(index) {
        this.currentSlide = index;
        this.updateSlide();
    }

    updateSlide() {
        const slide = this.slides[this.currentSlide];
        
        // Update slide visibility
        document.querySelectorAll('.slide').forEach((el, index) => {
            el.classList.toggle('active', index === this.currentSlide);
        });

        // Update content with animation
        this.animateContentChange(slide);

        // Update indicators
        document.querySelectorAll('.indicator').forEach((el, index) => {
            el.classList.toggle('active', index === this.currentSlide);
        });

        // Update theme mood if available
        if (window.themeController) {
            window.themeController.setMood(slide.mood);
        }

        // Restart progress bar
        this.restartProgressBar();
    }

    animateContentChange(slide) {
        const content = document.getElementById('slideContent');
        if (!content) return;

        content.style.opacity = '0';
        content.style.transform = 'translateY(20px)';

        setTimeout(() => {
            document.getElementById('slideCategory').textContent = slide.type.toUpperCase();
            document.getElementById('slideTitle').textContent = slide.title;
            document.getElementById('slideSubtitle').textContent = slide.subtitle;
            document.getElementById('slideDescription').textContent = slide.description;
            document.getElementById('slideCTA').textContent = slide.cta;
            document.getElementById('slideCTA').href = slide.link;

            content.style.opacity = '1';
            content.style.transform = 'translateY(0)';
        }, 150);
    }

    pausePlay() {
        if (this.isPlaying) {
            this.isPlaying = false;
            this.pauseSlideshow();
            document.getElementById('pausePlayText').textContent = 'PLAY';
        } else {
            this.isPlaying = true;
            this.startSlideshow();
            document.getElementById('pausePlayText').textContent = 'PAUSE';
        }
    }

    startProgressBar() {
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.style.transition = `width ${this.slideDuration}ms linear`;
            
            setTimeout(() => {
                progressBar.style.width = '100%';
            }, 50);
        }
    }

    pauseProgressBar() {
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            const currentWidth = progressBar.offsetWidth;
            const containerWidth = progressBar.parentElement.offsetWidth;
            const percentage = (currentWidth / containerWidth) * 100;
            
            progressBar.style.transition = 'none';
            progressBar.style.width = percentage + '%';
        }
    }

    restartProgressBar() {
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            progressBar.style.transition = 'none';
            progressBar.style.width = '0%';
            
            setTimeout(() => {
                progressBar.style.transition = `width ${this.slideDuration}ms linear`;
                progressBar.style.width = '100%';
            }, 50);
        }
    }

    preloadImages() {
        this.slides.forEach(slide => {
            const img1 = new Image();
            const img2 = new Image();
            img1.src = slide.image;
            img2.src = slide.overlay;
        });
    }

    addSlideshowStyles() {
        const styles = `
            .streetwear-slideshow {
                position: relative;
                height: 100vh;
                overflow: hidden;
            }

            .slideshow-container {
                position: relative;
                width: 100%;
                height: 100%;
            }

            .slides-wrapper {
                position: relative;
                width: 100%;
                height: 100%;
            }

            .slide {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0;
                transition: opacity 0.8s ease-in-out;
            }

            .slide.active {
                opacity: 1;
            }

            .slide-background {
                position: relative;
                width: 100%;
                height: 100%;
            }

            .slide-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                filter: contrast(1.2) saturate(1.1);
            }

            .slide-gradient {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.6) 100%);
            }

            .slide-art-overlay {
                position: absolute;
                top: 20%;
                right: 10%;
                width: 200px;
                height: 200px;
                opacity: 0.3;
                mix-blend-mode: overlay;
            }

            .art-overlay {
                width: 100%;
                height: 100%;
                object-fit: cover;
                filter: contrast(2) brightness(1.5);
            }

            .slideshow-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                z-index: 2;
            }

            .slide-content {
                max-width: 600px;
                transition: all 0.3s ease;
            }

            .slide-category {
                font-size: 0.875rem;
                font-weight: 700;
                letter-spacing: 0.2em;
                color: #ff0000;
                margin-bottom: 1rem;
                text-transform: uppercase;
            }

            .slide-title {
                font-size: clamp(3rem, 8vw, 6rem);
                font-weight: 900;
                line-height: 0.9;
                margin-bottom: 1rem;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            }

            .slide-subtitle {
                font-size: clamp(1.25rem, 3vw, 2rem);
                font-weight: 400;
                margin-bottom: 1.5rem;
                color: #e8e8e8;
            }

            .slide-description {
                font-size: 1.125rem;
                line-height: 1.6;
                margin-bottom: 2rem;
                color: #e8e8e8;
                max-width: 500px;
            }

            .slide-cta {
                display: flex;
                gap: 1rem;
                align-items: center;
            }

            .slideshow-controls {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 100%;
                display: flex;
                justify-content: space-between;
                padding: 0 2rem;
                z-index: 3;
            }

            .slide-nav {
                width: 60px;
                height: 60px;
                background: rgba(0,0,0,0.5);
                border: 2px solid #f8f8f8;
                color: #f8f8f8;
                cursor: pointer;
                transition: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.25rem;
            }

            .slide-nav:hover {
                background: #ff0000;
                border-color: #ff0000;
                transform: scale(1.1);
            }

            .slide-indicators {
                position: absolute;
                bottom: 2rem;
                left: 50%;
                transform: translateX(-50%);
                display: flex;
                gap: 1rem;
                z-index: 3;
            }

            .indicator {
                width: 60px;
                height: 4px;
                background: rgba(255,255,255,0.3);
                border: none;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .indicator.active {
                background: var(--street-red);
            }

            .slide-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: rgba(255,255,255,0.2);
                z-index: 3;
            }

            .progress-bar {
                height: 100%;
                background: var(--street-red);
                width: 0%;
            }

            @media (max-width: 768px) {
                .slide-content {
                    padding: 0 1rem;
                }
                
                .slide-cta {
                    flex-direction: column;
                    align-items: flex-start;
                }
                
                .slideshow-controls {
                    padding: 0 1rem;
                }
                
                .slide-nav {
                    width: 50px;
                    height: 50px;
                }
                
                .slide-art-overlay {
                    display: none;
                }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }
}

// Initialize slideshow when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.streetwareSlideshow = new StreetwareSlideshow();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StreetwareSlideshow;
}
