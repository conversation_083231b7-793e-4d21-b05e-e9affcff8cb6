// Perfume Data
const perfumeProducts = [
    {
        id: 1,
        name: "Midnight Essence",
        type: "eau-de-parfum",
        scent: "woody",
        price: 149.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1594035910387-fea47794261f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 89,
        sizes: ["30ml", "50ml", "100ml"],
        occasions: ["evening", "special"],
        badge: "bestseller",
        inStock: true,
        description: "A sophisticated blend of sandalwood, vanilla, and dark berries"
    },
    {
        id: 2,
        name: "Urban Fresh",
        type: "eau-de-toilette",
        scent: "fresh",
        price: 89.99,
        originalPrice: 109.99,
        image: "https://images.unsplash.com/photo-1541643600914-78b084683601?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 156,
        sizes: ["50ml", "100ml"],
        occasions: ["daily", "office"],
        badge: "new",
        inStock: true,
        description: "Crisp citrus notes with hints of mint and ocean breeze"
    },
    {
        id: 3,
        name: "Royal Oud",
        type: "perfume-oil",
        scent: "oriental",
        price: 299.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1588405748880-12d1d2a59db9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 45,
        sizes: ["30ml", "50ml"],
        occasions: ["evening", "special"],
        badge: "limited",
        inStock: true,
        description: "Luxurious oud with rose petals and amber undertones"
    },
    {
        id: 4,
        name: "Citrus Burst",
        type: "cologne",
        scent: "citrus",
        price: 65.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1563170351-be82bc888aa4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 203,
        sizes: ["50ml", "100ml", "150ml"],
        occasions: ["daily", "office"],
        badge: null,
        inStock: true,
        description: "Energizing blend of lemon, bergamot, and grapefruit"
    },
    {
        id: 5,
        name: "Velvet Rose",
        type: "eau-de-parfum",
        scent: "floral",
        price: 179.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1592945403244-b3fbafd7f539?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 78,
        sizes: ["30ml", "50ml", "100ml"],
        occasions: ["evening", "special"],
        badge: "bestseller",
        inStock: true,
        description: "Elegant rose bouquet with peony and white musk"
    },
    {
        id: 6,
        name: "Black Cedar",
        type: "eau-de-parfum",
        scent: "woody",
        price: 199.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1615634260167-c8cdede054de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 134,
        sizes: ["50ml", "100ml"],
        occasions: ["daily", "evening"],
        badge: null,
        inStock: true,
        description: "Bold cedarwood with black pepper and leather accents"
    }
];

// DOM Elements
const productsGrid = document.getElementById('productsGrid');
const filterToggle = document.getElementById('filterToggle');
const filterPanel = document.getElementById('filterPanel');
const sortSelect = document.getElementById('sortSelect');
const loadMoreBtn = document.getElementById('loadMoreBtn');
const priceRange = document.getElementById('priceRange');
const priceValue = document.getElementById('priceValue');

// State
let currentProducts = [...perfumeProducts];
let displayedProducts = 6;
let filters = {
    types: ['eau-de-parfum'],
    scents: [],
    sizes: ['50ml'],
    occasions: [],
    priceMax: 300,
    sortBy: 'featured'
};

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    renderProducts();
    setupEventListeners();
    updateFiltersFromState();
});

// Event Listeners
function setupEventListeners() {
    // Filter toggle
    filterToggle.addEventListener('click', () => {
        filterPanel.classList.toggle('active');
        filterToggle.classList.toggle('active');
    });

    // Sort select
    sortSelect.addEventListener('change', (e) => {
        filters.sortBy = e.target.value;
        applyFilters();
    });

    // Price range
    if (priceRange) {
        priceRange.addEventListener('input', (e) => {
            const value = e.target.value;
            priceValue.textContent = `$${value}`;
            filters.priceMax = parseInt(value);
        });

        priceRange.addEventListener('change', () => {
            applyFilters();
        });
    }

    // Type checkboxes
    document.querySelectorAll('input[name="type"]').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const type = e.target.value;
            if (e.target.checked) {
                if (!filters.types.includes(type)) {
                    filters.types.push(type);
                }
            } else {
                filters.types = filters.types.filter(t => t !== type);
            }
            applyFilters();
        });
    });

    // Scent checkboxes
    document.querySelectorAll('input[name="scent"]').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const scent = e.target.value;
            if (e.target.checked) {
                if (!filters.scents.includes(scent)) {
                    filters.scents.push(scent);
                }
            } else {
                filters.scents = filters.scents.filter(s => s !== scent);
            }
            applyFilters();
        });
    });

    // Occasion checkboxes
    document.querySelectorAll('input[name="occasion"]').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const occasion = e.target.value;
            if (e.target.checked) {
                if (!filters.occasions.includes(occasion)) {
                    filters.occasions.push(occasion);
                }
            } else {
                filters.occasions = filters.occasions.filter(o => o !== occasion);
            }
            applyFilters();
        });
    });

    // Size buttons
    document.querySelectorAll('.size-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const size = e.target.textContent;
            
            // Toggle active state
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                if (!filters.sizes.includes(size)) {
                    filters.sizes.push(size);
                }
            } else {
                filters.sizes = filters.sizes.filter(s => s !== size);
            }
            applyFilters();
        });
    });

    // Clear filters
    document.querySelector('.clear-filters').addEventListener('click', () => {
        clearAllFilters();
    });

    // Apply filters
    document.querySelector('.apply-filters').addEventListener('click', () => {
        applyFilters();
        filterPanel.classList.remove('active');
        filterToggle.classList.remove('active');
    });

    // Load more
    loadMoreBtn.addEventListener('click', () => {
        displayedProducts += 6;
        renderProducts();
    });
}

// Update filters UI from state
function updateFiltersFromState() {
    // Update type checkboxes
    document.querySelectorAll('input[name="type"]').forEach(checkbox => {
        checkbox.checked = filters.types.includes(checkbox.value);
    });

    // Update size buttons
    document.querySelectorAll('.size-btn').forEach(btn => {
        if (filters.sizes.includes(btn.textContent)) {
            btn.classList.add('active');
        }
    });

    // Update price range
    if (priceRange) {
        priceRange.value = filters.priceMax;
        priceValue.textContent = `$${filters.priceMax}`;
    }
}

// Apply filters and sorting
function applyFilters() {
    let filtered = [...perfumeProducts];

    // Filter by types
    if (filters.types.length > 0) {
        filtered = filtered.filter(product => 
            filters.types.includes(product.type)
        );
    }

    // Filter by scents
    if (filters.scents.length > 0) {
        filtered = filtered.filter(product => 
            filters.scents.includes(product.scent)
        );
    }

    // Filter by sizes
    if (filters.sizes.length > 0) {
        filtered = filtered.filter(product => 
            product.sizes.some(size => filters.sizes.includes(size))
        );
    }

    // Filter by occasions
    if (filters.occasions.length > 0) {
        filtered = filtered.filter(product => 
            product.occasions.some(occasion => filters.occasions.includes(occasion))
        );
    }

    // Filter by price
    filtered = filtered.filter(product => product.price <= filters.priceMax);

    // Sort products
    switch (filters.sortBy) {
        case 'newest':
            filtered.sort((a, b) => b.id - a.id);
            break;
        case 'price-low':
            filtered.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            filtered.sort((a, b) => b.price - a.price);
            break;
        case 'rating':
            filtered.sort((a, b) => b.rating - a.rating);
            break;
        default:
            // Featured - keep original order
            break;
    }

    currentProducts = filtered;
    displayedProducts = 6;
    renderProducts();
    updateResultsCount();
}

// Clear all filters
function clearAllFilters() {
    filters = {
        types: [],
        scents: [],
        sizes: [],
        occasions: [],
        priceMax: 500,
        sortBy: 'featured'
    };

    // Update UI
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });

    document.querySelectorAll('.size-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    if (priceRange) {
        priceRange.value = 500;
        priceValue.textContent = '$500';
    }

    sortSelect.value = 'featured';

    applyFilters();
}

// Update results count
function updateResultsCount() {
    const resultsCount = document.querySelector('.results-count span');
    const showing = Math.min(displayedProducts, currentProducts.length);
    resultsCount.textContent = `Showing ${showing} of ${currentProducts.length} fragrances`;
}

// Render products
function renderProducts() {
    const productsToShow = currentProducts.slice(0, displayedProducts);
    
    productsGrid.innerHTML = productsToShow.map(product => `
        <div class="product-card" data-product-id="${product.id}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" loading="lazy">
                <div class="product-overlay">
                    <button class="quick-view-btn" onclick="quickView(${product.id})">
                        <i class="fas fa-eye"></i>
                        Quick View
                    </button>
                    <button class="add-to-cart-btn" onclick="addToCart(${product.id})">
                        <i class="fas fa-shopping-cart"></i>
                        Add to Cart
                    </button>
                    <button class="wishlist-btn-product" onclick="toggleWishlist(${product.id})">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>
                ${product.badge ? `<span class="product-badge ${product.badge}">${product.badge}</span>` : ''}
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-category">${formatType(product.type)} • ${formatScent(product.scent)}</p>
                <div class="product-price">
                    <span class="current-price">$${product.price}</span>
                    ${product.originalPrice ? `<span class="original-price">$${product.originalPrice}</span>` : ''}
                </div>
                <div class="product-rating">
                    <div class="stars">
                        ${generateStars(product.rating)}
                    </div>
                    <span class="rating-count">(${product.reviews})</span>
                </div>
            </div>
        </div>
    `).join('');

    // Show/hide load more button
    if (displayedProducts >= currentProducts.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'inline-flex';
    }

    updateResultsCount();
}

// Helper functions
function formatType(type) {
    return type.split('-').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
}

function formatScent(scent) {
    return scent.charAt(0).toUpperCase() + scent.slice(1);
}

// Generate star rating HTML
function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    return stars;
}

// Product actions
function quickView(productId) {
    const product = perfumeProducts.find(p => p.id === productId);
    alert(`Quick view for: ${product.name}\nType: ${formatType(product.type)}\nScent: ${formatScent(product.scent)}\nPrice: $${product.price}\nDescription: ${product.description}`);
}

function addToCart(productId) {
    const product = perfumeProducts.find(p => p.id === productId);
    alert(`Added "${product.name}" to cart!`);
    
    // Update cart badge
    const cartBadge = document.querySelector('.cart-btn .badge');
    if (cartBadge) {
        const currentCount = parseInt(cartBadge.textContent);
        cartBadge.textContent = currentCount + 1;
    }
}

function toggleWishlist(productId) {
    const product = perfumeProducts.find(p => p.id === productId);
    alert(`Toggled wishlist for: ${product.name}`);
    
    // Update wishlist badge
    const wishlistBadge = document.querySelector('.wishlist-btn .badge');
    if (wishlistBadge) {
        const currentCount = parseInt(wishlistBadge.textContent);
        wishlistBadge.textContent = Math.max(0, currentCount + (Math.random() > 0.5 ? 1 : -1));
    }
}
