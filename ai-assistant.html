<!-- AI Assistant Widget -->
<div class="ai-assistant" id="aiAssistant">
    <div class="ai-toggle" id="aiToggle">
        <div class="ai-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="ai-notification" id="aiNotification">1</div>
    </div>

    <div class="ai-chat-window" id="aiChatWindow">
        <div class="ai-header">
            <div class="ai-info">
                <div class="ai-avatar-large">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ai-details">
                    <h3>Magna AI</h3>
                    <p class="ai-status">Online • Ready to help</p>
                </div>
            </div>
            <div class="ai-actions">
                <button class="ai-action-btn" id="aiMinimize">
                    <i class="fas fa-minus"></i>
                </button>
                <button class="ai-action-btn" id="aiClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="ai-chat-body" id="aiChatBody">
            <div class="ai-welcome-message">
                <div class="ai-message ai-message-bot">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>👋 Hi! I'm Magna AI, your personal shopping assistant. I can help you with:</p>
                        <ul>
                            <li>🛍️ Finding the perfect products</li>
                            <li>📏 Size recommendations</li>
                            <li>🚚 Order tracking</li>
                            <li>💬 General questions</li>
                        </ul>
                        <p>How can I assist you today?</p>
                    </div>
                </div>
            </div>

            <div class="ai-quick-actions">
                <button class="quick-action-btn" data-action="size-guide">
                    <i class="fas fa-ruler"></i>
                    Size Guide
                </button>
                <button class="quick-action-btn" data-action="track-order">
                    <i class="fas fa-truck"></i>
                    Track Order
                </button>
                <button class="quick-action-btn" data-action="recommendations">
                    <i class="fas fa-star"></i>
                    Get Recommendations
                </button>
                <button class="quick-action-btn" data-action="support">
                    <i class="fas fa-headset"></i>
                    Contact Support
                </button>
            </div>

            <div class="ai-messages" id="aiMessages">
                <!-- Chat messages will appear here -->
            </div>
        </div>

        <div class="ai-input-area">
            <div class="ai-typing-indicator" id="aiTyping">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span>Magna AI is typing...</span>
            </div>
            
            <div class="ai-input-container">
                <input type="text" class="ai-input" id="aiInput" placeholder="Type your message...">
                <button class="ai-send-btn" id="aiSendBtn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            
            <div class="ai-suggestions" id="aiSuggestions">
                <!-- Dynamic suggestions will appear here -->
            </div>
        </div>
    </div>
</div>

<!-- AI Assistant Styles -->
<style>
.ai-assistant {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: var(--font-primary);
}

.ai-toggle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(14, 165, 233, 0.3);
    transition: var(--transition-normal);
    position: relative;
}

.ai-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(14, 165, 233, 0.4);
}

.ai-avatar {
    color: white;
    font-size: 1.5rem;
}

.ai-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background: var(--accent-500);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
}

.ai-chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 380px;
    height: 500px;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.ai-chat-window.active {
    display: flex;
}

.ai-header {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ai-avatar-large {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.ai-details h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.ai-status {
    font-size: 0.875rem;
    opacity: 0.9;
}

.ai-actions {
    display: flex;
    gap: 0.5rem;
}

.ai-action-btn {
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.ai-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.ai-chat-body {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.ai-message {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.ai-message-user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.ai-message-bot .message-avatar {
    background: var(--primary-100);
    color: var(--primary-600);
}

.ai-message-user .message-avatar {
    background: var(--neutral-200);
    color: var(--neutral-600);
}

.message-content {
    background: var(--neutral-100);
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    max-width: 80%;
}

.ai-message-user .message-content {
    background: var(--primary-500);
    color: white;
}

.message-content p {
    margin-bottom: 0.5rem;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: 0.5rem 0;
    padding-left: 1rem;
}

.ai-quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.quick-action-btn {
    padding: 0.75rem;
    background: var(--neutral-50);
    border: 1px solid var(--neutral-200);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--neutral-700);
}

.quick-action-btn:hover {
    background: var(--primary-50);
    border-color: var(--primary-200);
    color: var(--primary-700);
}

.ai-input-area {
    border-top: 1px solid var(--neutral-200);
    padding: 1rem;
}

.ai-typing-indicator {
    display: none;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--neutral-500);
    font-size: 0.875rem;
}

.ai-typing-indicator.active {
    display: flex;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: var(--neutral-400);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.ai-input-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.ai-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: 1.5rem;
    outline: none;
    transition: var(--transition-fast);
}

.ai-input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.ai-send-btn {
    width: 40px;
    height: 40px;
    background: var(--primary-500);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.ai-send-btn:hover {
    background: var(--primary-600);
    transform: scale(1.05);
}

.ai-suggestions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.suggestion-btn {
    padding: 0.5rem 0.75rem;
    background: var(--neutral-100);
    border: 1px solid var(--neutral-200);
    border-radius: 1rem;
    font-size: 0.875rem;
    color: var(--neutral-700);
    cursor: pointer;
    transition: var(--transition-fast);
}

.suggestion-btn:hover {
    background: var(--primary-100);
    border-color: var(--primary-300);
    color: var(--primary-700);
}

/* Mobile Responsive */
@media (max-width: 480px) {
    .ai-chat-window {
        width: calc(100vw - 40px);
        height: 70vh;
        bottom: 80px;
        right: 20px;
        left: 20px;
    }
    
    .ai-quick-actions {
        grid-template-columns: 1fr;
    }
}
</style>
