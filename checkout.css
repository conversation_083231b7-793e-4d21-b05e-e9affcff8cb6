/* Checkout Styles */

.checkout-section {
    padding: 2rem 0;
    background: var(--neutral-50);
    min-height: 80vh;
}

/* Header Styles */
.navbar .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.checkout-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--neutral-400);
    font-size: 0.875rem;
    font-weight: 500;
}

.progress-step.active {
    color: var(--primary-600);
}

.step-number {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--neutral-300);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: var(--transition-fast);
}

.progress-step.active .step-number {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
}

.progress-line {
    width: 3rem;
    height: 2px;
    background: var(--neutral-300);
    margin-top: -1rem;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-600);
    font-weight: 500;
    font-size: 0.875rem;
}

/* Layout */
.checkout-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

/* Checkout Form */
.checkout-form {
    background: white;
    border-radius: 1rem;
    padding: 2.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

.step-header {
    margin-bottom: 2rem;
    text-align: center;
}

.step-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
}

.step-header p {
    color: var(--neutral-600);
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--neutral-200);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: var(--neutral-700);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: var(--transition-fast);
    outline: none;
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Shipping Options */
.shipping-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.shipping-option {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 2px solid var(--neutral-200);
    border-radius: 0.75rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.shipping-option:hover {
    border-color: var(--neutral-300);
}

.shipping-option input[type="radio"] {
    margin-right: 1rem;
}

.shipping-option input[type="radio"]:checked + .option-content {
    color: var(--primary-700);
}

.shipping-option:has(input[type="radio"]:checked) {
    border-color: var(--primary-500);
    background: var(--primary-50);
}

.option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.option-info h4 {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.option-info p {
    color: var(--neutral-600);
    font-size: 0.875rem;
}

.option-price {
    font-weight: 600;
    font-size: 1.125rem;
}

/* Payment Methods */
.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.payment-method {
    border: 2px solid var(--neutral-200);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: var(--transition-fast);
}

.payment-method.active {
    border-color: var(--primary-500);
}

.method-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--neutral-50);
    cursor: pointer;
}

.method-header input[type="radio"] {
    margin: 0;
}

.method-header h3 {
    flex: 1;
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
}

.card-icons,
.mobile-icons,
.wallet-icons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    color: var(--neutral-500);
}

.mobile-provider {
    padding: 0.25rem 0.5rem;
    background: var(--neutral-200);
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.method-content {
    padding: 1.5rem;
    display: none;
}

.payment-method.active .method-content {
    display: block;
}

.mobile-info,
.paypal-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--primary-50);
    border-radius: 0.5rem;
    color: var(--primary-700);
}

.wallet-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.wallet-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    background: white;
    color: var(--neutral-700);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.wallet-btn:hover {
    background: var(--neutral-50);
    border-color: var(--neutral-400);
}

/* Order Review */
.order-review {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.review-section {
    padding: 1.5rem;
    background: var(--neutral-50);
    border-radius: 0.75rem;
    position: relative;
}

.review-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1rem;
}

.review-content {
    color: var(--neutral-700);
    line-height: 1.6;
}

.edit-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    background: var(--primary-500);
    color: white;
    border: none;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.edit-btn:hover {
    background: var(--primary-600);
}

.terms-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--neutral-50);
    border-radius: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: var(--neutral-700);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-label .checkmark {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: 0.25rem;
    position: relative;
    transition: var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

/* Step Actions */
.step-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--neutral-200);
}

.step-actions .btn {
    min-width: 150px;
}

/* Order Summary */
.order-summary {
    position: sticky;
    top: 2rem;
}

.summary-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.summary-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1.5rem;
}

.order-items {
    margin-bottom: 1.5rem;
}

.order-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--neutral-200);
}

.order-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 4rem;
    height: 4rem;
    border-radius: 0.5rem;
    overflow: hidden;
    flex-shrink: 0;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.item-details p {
    color: var(--neutral-600);
    font-size: 0.875rem;
}

.item-price {
    font-weight: 600;
    color: var(--neutral-900);
}

.summary-calculations {
    margin: 1.5rem 0;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    color: var(--neutral-700);
}

.summary-row.total {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0;
}

.summary-divider {
    height: 1px;
    background: var(--neutral-200);
    margin: 1rem 0;
}

.promo-section {
    margin: 1.5rem 0;
}

.promo-input-group {
    display: flex;
    gap: 0.5rem;
}

.promo-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    outline: none;
}

.promo-input:focus {
    border-color: var(--primary-500);
}

.promo-btn {
    padding: 0.75rem 1rem;
    background: var(--neutral-900);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.promo-btn:hover {
    background: var(--neutral-800);
}

.security-features {
    display: flex;
    justify-content: space-around;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--neutral-200);
}

.security-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-600);
    font-size: 0.875rem;
    text-align: center;
}

.security-item i {
    font-size: 1.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .checkout-progress {
        display: none;
    }
    
    .checkout-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .checkout-form {
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .step-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .step-actions .btn {
        width: 100%;
    }
    
    .security-features {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .checkout-form {
        padding: 1rem;
    }
    
    .summary-card {
        padding: 1.5rem;
    }
    
    .shipping-option,
    .method-header {
        padding: 0.75rem;
    }
    
    .method-content {
        padding: 1rem;
    }
}
