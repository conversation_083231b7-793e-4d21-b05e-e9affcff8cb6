// Contact Form Handler
class ContactFormHandler {
    constructor() {
        this.form = document.getElementById('contactForm');
        this.setupEventListeners();
        this.setupFormValidation();
    }

    setupEventListeners() {
        if (this.form) {
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        }

        // Real-time validation
        const inputs = this.form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });

        // Subject change handler
        const subjectSelect = document.getElementById('subject');
        if (subjectSelect) {
            subjectSelect.addEventListener('change', () => this.handleSubjectChange());
        }
    }

    setupFormValidation() {
        // Custom validation messages
        this.validationRules = {
            firstName: {
                required: true,
                minLength: 2,
                pattern: /^[a-zA-Z\s]+$/,
                message: 'First name must be at least 2 characters and contain only letters'
            },
            lastName: {
                required: true,
                minLength: 2,
                pattern: /^[a-zA-Z\s]+$/,
                message: 'Last name must be at least 2 characters and contain only letters'
            },
            email: {
                required: true,
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'Please enter a valid email address'
            },
            phone: {
                required: false,
                pattern: /^[\+]?[1-9][\d]{0,15}$/,
                message: 'Please enter a valid phone number'
            },
            subject: {
                required: true,
                message: 'Please select a subject'
            },
            message: {
                required: true,
                minLength: 10,
                maxLength: 1000,
                message: 'Message must be between 10 and 1000 characters'
            }
        };
    }

    validateField(field) {
        const fieldName = field.name;
        const value = field.value.trim();
        const rules = this.validationRules[fieldName];

        if (!rules) return true;

        // Clear previous errors
        this.clearFieldError(field);

        // Required field validation
        if (rules.required && !value) {
            this.showFieldError(field, `${this.getFieldLabel(field)} is required`);
            return false;
        }

        // Skip other validations if field is empty and not required
        if (!value && !rules.required) return true;

        // Length validation
        if (rules.minLength && value.length < rules.minLength) {
            this.showFieldError(field, rules.message);
            return false;
        }

        if (rules.maxLength && value.length > rules.maxLength) {
            this.showFieldError(field, rules.message);
            return false;
        }

        // Pattern validation
        if (rules.pattern && !rules.pattern.test(value)) {
            this.showFieldError(field, rules.message);
            return false;
        }

        // Field-specific validations
        if (fieldName === 'email' && value) {
            return this.validateEmail(field, value);
        }

        return true;
    }

    validateEmail(field, email) {
        // Additional email validation
        const commonDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com'];
        const domain = email.split('@')[1];
        
        if (domain && !commonDomains.includes(domain) && !domain.includes('.')) {
            this.showFieldError(field, 'Please enter a valid email address');
            return false;
        }

        return true;
    }

    showFieldError(field, message) {
        field.classList.add('error');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Add new error message
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.textContent = message;
        field.parentNode.appendChild(errorElement);
    }

    clearFieldError(field) {
        field.classList.remove('error');
        const errorMessage = field.parentNode.querySelector('.error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }

    getFieldLabel(field) {
        const label = field.parentNode.querySelector('label');
        return label ? label.textContent.replace('*', '').trim() : field.name;
    }

    handleSubjectChange() {
        const subject = document.getElementById('subject').value;
        const orderNumberGroup = document.getElementById('orderNumber').parentNode;
        
        // Show/hide order number field based on subject
        if (subject === 'order' || subject === 'shipping' || subject === 'return') {
            orderNumberGroup.style.display = 'flex';
            document.getElementById('orderNumber').required = true;
        } else {
            orderNumberGroup.style.display = 'none';
            document.getElementById('orderNumber').required = false;
        }
    }

    async handleSubmit(e) {
        e.preventDefault();

        // Validate all fields
        const formData = new FormData(this.form);
        const isValid = this.validateForm();

        if (!isValid) {
            this.showFormError('Please correct the errors above and try again.');
            return;
        }

        // Show loading state
        const submitBtn = this.form.querySelector('.submit-btn');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        submitBtn.disabled = true;

        try {
            // Simulate API call (replace with actual endpoint)
            const response = await this.submitForm(formData);
            
            if (response.success) {
                this.showSuccessMessage();
                this.form.reset();
                
                // Track successful submission
                if (window.magnaAI) {
                    window.magnaAI.logActivity('contact_form_submitted', {
                        subject: formData.get('subject'),
                        hasOrderNumber: !!formData.get('orderNumber')
                    });
                }
            } else {
                throw new Error(response.message || 'Failed to send message');
            }
        } catch (error) {
            this.showFormError('Failed to send message. Please try again or contact us directly.');
            console.error('Contact form error:', error);
        } finally {
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    validateForm() {
        const inputs = this.form.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    async submitForm(formData) {
        // Simulate API call - replace with actual backend endpoint
        return new Promise((resolve) => {
            setTimeout(() => {
                // Simulate success/failure
                const success = Math.random() > 0.1; // 90% success rate
                resolve({
                    success: success,
                    message: success ? 'Message sent successfully' : 'Server error'
                });
            }, 2000);
        });
    }

    showSuccessMessage() {
        // Remove any existing messages
        this.clearFormMessages();

        const successElement = document.createElement('div');
        successElement.className = 'form-message success-message';
        successElement.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <div>
                <h4>Message Sent Successfully!</h4>
                <p>Thank you for contacting us. We'll get back to you within 2 hours.</p>
            </div>
        `;

        this.form.parentNode.insertBefore(successElement, this.form);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            successElement.remove();
        }, 5000);
    }

    showFormError(message) {
        // Remove any existing messages
        this.clearFormMessages();

        const errorElement = document.createElement('div');
        errorElement.className = 'form-message error-message';
        errorElement.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <div>
                <h4>Error</h4>
                <p>${message}</p>
            </div>
        `;

        this.form.parentNode.insertBefore(errorElement, this.form);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            errorElement.remove();
        }, 5000);
    }

    clearFormMessages() {
        const messages = this.form.parentNode.querySelectorAll('.form-message');
        messages.forEach(message => message.remove());
    }
}

// Initialize contact form handler
document.addEventListener('DOMContentLoaded', () => {
    new ContactFormHandler();
});

// Add CSS for form validation and messages
const style = document.createElement('style');
style.textContent = `
    .form-group input.error,
    .form-group select.error,
    .form-group textarea.error {
        border-color: var(--accent-500);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .error-message {
        color: var(--accent-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .error-message::before {
        content: '⚠';
        font-size: 0.75rem;
    }

    .form-message {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
        animation: slideIn 0.3s ease-out;
    }

    .form-message.success-message {
        background: var(--primary-50);
        border: 1px solid var(--primary-200);
        color: var(--primary-800);
    }

    .form-message.error-message {
        background: var(--accent-50);
        border: 1px solid var(--accent-200);
        color: var(--accent-800);
    }

    .form-message i {
        font-size: 1.5rem;
        flex-shrink: 0;
    }

    .form-message h4 {
        margin: 0 0 0.25rem 0;
        font-weight: 600;
    }

    .form-message p {
        margin: 0;
        font-size: 0.875rem;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
