// AI Style Assistant - Mood-Reactive Fashion AI
class AIStyleAssistant {
    constructor() {
        this.userProfile = this.loadUserProfile();
        this.currentMood = 'energetic';
        this.stylePreferences = this.loadStylePreferences();
        this.bodyType = this.userProfile.bodyType || 'not-specified';
        this.colorPalette = this.generateColorPalette();
        
        this.moodStyles = {
            energetic: {
                colors: ['#ff0080', '#00f5ff', '#ffff00', '#39ff14'],
                styles: ['streetwear', 'athletic', 'bold'],
                patterns: ['geometric', 'graffiti', 'neon'],
                vibes: ['confident', 'dynamic', 'urban']
            },
            calm: {
                colors: ['#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'],
                styles: ['minimalist', 'casual', 'natural'],
                patterns: ['organic', 'flowing', 'subtle'],
                vibes: ['peaceful', 'balanced', 'zen']
            },
            mysterious: {
                colors: ['#bf00ff', '#8000ff', '#4000ff', '#000000'],
                styles: ['gothic', 'avant-garde', 'dramatic'],
                patterns: ['abstract', 'dark', 'complex'],
                vibes: ['enigmatic', 'sophisticated', 'edgy']
            }
        };
        
        this.init();
    }

    init() {
        this.createStyleAssistantUI();
        this.setupMoodDetection();
        this.setupPersonalizationEngine();
        this.listenForThemeChanges();
    }

    createStyleAssistantUI() {
        const assistantHTML = `
            <div class="ai-style-assistant" id="aiStyleAssistant">
                <div class="style-toggle" id="styleToggle">
                    <div class="style-avatar">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="style-notification" id="styleNotification">!</div>
                </div>

                <div class="style-panel" id="stylePanel">
                    <div class="style-header">
                        <h3>AI Style Assistant</h3>
                        <p class="mood-indicator">Current Mood: <span id="currentMoodDisplay">${this.currentMood}</span></p>
                        <button class="close-style-btn" id="closeStyleBtn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="style-content">
                        <!-- Mood Selector -->
                        <div class="mood-section">
                            <h4>How are you feeling?</h4>
                            <div class="mood-options">
                                <button class="mood-option energetic" data-mood="energetic">
                                    <i class="fas fa-bolt"></i>
                                    <span>Energetic</span>
                                </button>
                                <button class="mood-option calm" data-mood="calm">
                                    <i class="fas fa-leaf"></i>
                                    <span>Calm</span>
                                </button>
                                <button class="mood-option mysterious" data-mood="mysterious">
                                    <i class="fas fa-moon"></i>
                                    <span>Mysterious</span>
                                </button>
                            </div>
                        </div>

                        <!-- Style Recommendations -->
                        <div class="recommendations-section" id="recommendationsSection">
                            <h4>Recommended for You</h4>
                            <div class="style-recommendations" id="styleRecommendations">
                                <!-- Dynamic recommendations will appear here -->
                            </div>
                        </div>

                        <!-- Color Palette -->
                        <div class="color-section">
                            <h4>Your Color Palette</h4>
                            <div class="color-palette" id="colorPalette">
                                <!-- Dynamic colors will appear here -->
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <button class="action-btn" onclick="aiStyleAssistant.generateOutfit()">
                                <i class="fas fa-tshirt"></i>
                                Generate Outfit
                            </button>
                            <button class="action-btn" onclick="aiStyleAssistant.findPerfume()">
                                <i class="fas fa-spray-can"></i>
                                Find Scent
                            </button>
                            <button class="action-btn" onclick="aiStyleAssistant.customDesign()">
                                <i class="fas fa-paint-brush"></i>
                                Custom Design
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', assistantHTML);
        this.setupStyleAssistantEvents();
        this.updateMoodDisplay();
    }

    setupStyleAssistantEvents() {
        const styleToggle = document.getElementById('styleToggle');
        const stylePanel = document.getElementById('stylePanel');
        const closeBtn = document.getElementById('closeStyleBtn');
        
        styleToggle.addEventListener('click', () => {
            stylePanel.classList.toggle('active');
            this.generateRecommendations();
        });

        closeBtn.addEventListener('click', () => {
            stylePanel.classList.remove('active');
        });

        // Mood selection
        document.querySelectorAll('.mood-option').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const mood = e.currentTarget.dataset.mood;
                this.setMood(mood);
            });
        });
    }

    setMood(mood) {
        this.currentMood = mood;
        this.updateMoodDisplay();
        this.generateRecommendations();
        this.updateColorPalette();
        
        // Update theme controller if available
        if (window.themeController) {
            window.themeController.setMood(mood);
        }
        
        // Save preference
        localStorage.setItem('ai-style-mood', mood);
        
        // Trigger mood-based product filtering
        this.filterProductsByMood(mood);
    }

    updateMoodDisplay() {
        const moodDisplay = document.getElementById('currentMoodDisplay');
        if (moodDisplay) {
            moodDisplay.textContent = this.currentMood;
            moodDisplay.className = `mood-${this.currentMood}`;
        }

        // Update active mood button
        document.querySelectorAll('.mood-option').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-mood="${this.currentMood}"]`)?.classList.add('active');
    }

    generateRecommendations() {
        const recommendations = this.getStyleRecommendations();
        const container = document.getElementById('styleRecommendations');
        
        if (container) {
            container.innerHTML = recommendations.map(item => `
                <div class="recommendation-item" data-product-id="${item.id}">
                    <div class="rec-image">
                        <img src="${item.image}" alt="${item.name}" loading="lazy">
                    </div>
                    <div class="rec-info">
                        <h5>${item.name}</h5>
                        <p class="rec-reason">${item.reason}</p>
                        <div class="rec-actions">
                            <button class="rec-btn view-btn" onclick="aiStyleAssistant.viewProduct(${item.id})">
                                View
                            </button>
                            <button class="rec-btn add-btn" onclick="aiStyleAssistant.addToOutfit(${item.id})">
                                Add to Outfit
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
    }

    getStyleRecommendations() {
        const moodStyle = this.moodStyles[this.currentMood];
        const recommendations = [];
        
        // Mock product data - in real app, this would come from your product database
        const products = [
            {
                id: 1,
                name: "Neon Pulse Hoodie",
                image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
                category: "clothing",
                style: "streetwear",
                colors: ["#ff0080", "#00f5ff"],
                mood: "energetic"
            },
            {
                id: 2,
                name: "Zen Garden Perfume",
                image: "https://images.unsplash.com/photo-1594035910387-fea47794261f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
                category: "perfumes",
                style: "natural",
                colors: ["#4ecdc4", "#96ceb4"],
                mood: "calm"
            },
            {
                id: 3,
                name: "Shadow Chain Necklace",
                image: "https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
                category: "jewelry",
                style: "gothic",
                colors: ["#bf00ff", "#000000"],
                mood: "mysterious"
            }
        ];

        // Filter products based on current mood and style preferences
        const filteredProducts = products.filter(product => {
            return product.mood === this.currentMood || 
                   moodStyle.styles.includes(product.style) ||
                   product.colors.some(color => moodStyle.colors.includes(color));
        });

        // Generate AI reasons for recommendations
        filteredProducts.forEach(product => {
            const reason = this.generateRecommendationReason(product);
            recommendations.push({
                ...product,
                reason: reason
            });
        });

        return recommendations.slice(0, 3); // Limit to top 3 recommendations
    }

    generateRecommendationReason(product) {
        const moodStyle = this.moodStyles[this.currentMood];
        const reasons = [
            `Perfect for your ${this.currentMood} mood`,
            `Matches your ${moodStyle.vibes[0]} vibe`,
            `Complements your color preferences`,
            `Trending in ${product.style} fashion`,
            `AI-selected for your body type`,
            `Enhances your ${this.currentMood} energy`
        ];
        
        return reasons[Math.floor(Math.random() * reasons.length)];
    }

    updateColorPalette() {
        const colors = this.moodStyles[this.currentMood].colors;
        const container = document.getElementById('colorPalette');
        
        if (container) {
            container.innerHTML = colors.map(color => `
                <div class="color-swatch" style="background: ${color}" title="${color}">
                    <span class="color-code">${color}</span>
                </div>
            `).join('');
        }
    }

    setupMoodDetection() {
        // Detect mood based on user behavior
        let scrollSpeed = 0;
        let clickFrequency = 0;
        let timeOfDay = new Date().getHours();
        
        // Track scroll behavior
        let lastScrollTime = Date.now();
        window.addEventListener('scroll', () => {
            const now = Date.now();
            scrollSpeed = Math.abs(window.scrollY - (window.lastScrollY || 0)) / (now - lastScrollTime);
            window.lastScrollY = window.scrollY;
            lastScrollTime = now;
        });

        // Track click behavior
        document.addEventListener('click', () => {
            clickFrequency++;
        });

        // Auto-detect mood every 30 seconds
        setInterval(() => {
            const detectedMood = this.detectMoodFromBehavior(scrollSpeed, clickFrequency, timeOfDay);
            if (detectedMood !== this.currentMood) {
                this.suggestMoodChange(detectedMood);
            }
            clickFrequency = 0; // Reset
        }, 30000);
    }

    detectMoodFromBehavior(scrollSpeed, clickFrequency, timeOfDay) {
        // High activity = energetic
        if (scrollSpeed > 5 || clickFrequency > 10) {
            return 'energetic';
        }
        
        // Late night/early morning = mysterious
        if (timeOfDay < 6 || timeOfDay > 22) {
            return 'mysterious';
        }
        
        // Default to calm for moderate activity
        return 'calm';
    }

    suggestMoodChange(suggestedMood) {
        const notification = document.getElementById('styleNotification');
        if (notification) {
            notification.style.display = 'flex';
            notification.title = `AI suggests ${suggestedMood} mood based on your activity`;
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }
    }

    generateOutfit() {
        const outfit = this.createCompleteOutfit();
        this.displayOutfitModal(outfit);
    }

    createCompleteOutfit() {
        const moodStyle = this.moodStyles[this.currentMood];
        
        return {
            top: {
                name: `${moodStyle.vibes[0]} ${this.currentMood} Top`,
                color: moodStyle.colors[0],
                style: moodStyle.styles[0]
            },
            bottom: {
                name: `${moodStyle.vibes[1]} ${this.currentMood} Bottom`,
                color: moodStyle.colors[1],
                style: moodStyle.styles[1]
            },
            accessories: {
                name: `${moodStyle.vibes[2]} Accessories`,
                color: moodStyle.colors[2],
                style: moodStyle.styles[2]
            },
            fragrance: {
                name: `${this.currentMood} Signature Scent`,
                notes: this.getScentNotes(this.currentMood)
            }
        };
    }

    getScentNotes(mood) {
        const scentProfiles = {
            energetic: ['citrus', 'mint', 'ginger', 'electric'],
            calm: ['lavender', 'vanilla', 'sandalwood', 'ocean'],
            mysterious: ['oud', 'dark berries', 'amber', 'smoke']
        };
        
        return scentProfiles[mood] || ['fresh', 'clean', 'modern'];
    }

    findPerfume() {
        const scentEngine = new ScentDiscoveryEngine(this.currentMood);
        scentEngine.findPerfumeByMood();
    }

    customDesign() {
        const designer = new CustomDesigner(this.currentMood, this.stylePreferences);
        designer.openDesignStudio();
    }

    filterProductsByMood(mood) {
        // Apply mood-based filtering to product grids
        const productCards = document.querySelectorAll('.product-card');
        const moodStyle = this.moodStyles[mood];
        
        productCards.forEach(card => {
            const productId = card.dataset.productId;
            // In real app, you'd check product data against mood preferences
            // For demo, we'll just add mood-based styling
            card.style.borderColor = moodStyle.colors[0];
            card.style.boxShadow = `0 0 20px ${moodStyle.colors[0]}33`;
        });
    }

    listenForThemeChanges() {
        document.addEventListener('moodChanged', (e) => {
            this.setMood(e.detail.mood);
        });
    }

    loadUserProfile() {
        const saved = localStorage.getItem('ai-style-profile');
        return saved ? JSON.parse(saved) : {
            bodyType: 'not-specified',
            stylePreferences: [],
            colorPreferences: [],
            sizePreferences: {}
        };
    }

    loadStylePreferences() {
        const saved = localStorage.getItem('ai-style-preferences');
        return saved ? JSON.parse(saved) : {
            favoriteStyles: ['streetwear'],
            avoidStyles: [],
            preferredColors: [],
            budgetRange: 'medium'
        };
    }

    saveUserProfile() {
        localStorage.setItem('ai-style-profile', JSON.stringify(this.userProfile));
        localStorage.setItem('ai-style-preferences', JSON.stringify(this.stylePreferences));
    }

    // Public API methods
    viewProduct(productId) {
        // Navigate to product page or open modal
        window.location.href = `product.html?id=${productId}`;
    }

    addToOutfit(productId) {
        // Add to virtual outfit builder
        console.log(`Added product ${productId} to outfit`);
        // In real app, this would update outfit state
    }

    generateColorPalette() {
        // Generate personalized color palette based on preferences
        return this.moodStyles[this.currentMood].colors;
    }
}

// Scent Discovery Engine
class ScentDiscoveryEngine {
    constructor(mood) {
        this.mood = mood;
        this.scentDatabase = this.loadScentDatabase();
    }

    findPerfumeByMood() {
        const recommendations = this.scentDatabase.filter(scent => 
            scent.mood === this.mood || scent.emotions.includes(this.mood)
        );
        
        this.displayScentRecommendations(recommendations);
    }

    loadScentDatabase() {
        return [
            {
                id: 1,
                name: "Electric Pulse",
                mood: "energetic",
                emotions: ["confident", "dynamic"],
                notes: ["citrus", "mint", "electric"],
                image: "https://images.unsplash.com/photo-1594035910387-fea47794261f"
            },
            {
                id: 2,
                name: "Zen Garden",
                mood: "calm",
                emotions: ["peaceful", "balanced"],
                notes: ["lavender", "vanilla", "sandalwood"],
                image: "https://images.unsplash.com/photo-1541643600914-78b084683601"
            }
        ];
    }

    displayScentRecommendations(scents) {
        // Create and show scent recommendation modal
        console.log('Scent recommendations:', scents);
    }
}

// Custom Designer
class CustomDesigner {
    constructor(mood, preferences) {
        this.mood = mood;
        this.preferences = preferences;
    }

    openDesignStudio() {
        // Open custom design interface
        console.log('Opening design studio for mood:', this.mood);
        // In real app, this would open a design interface
    }
}

// Initialize AI Style Assistant
document.addEventListener('DOMContentLoaded', () => {
    window.aiStyleAssistant = new AIStyleAssistant();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIStyleAssistant;
}
