// Cart Data
let cartItems = [
    {
        id: 1,
        name: "Urban Legend Hoodie",
        category: "Clothing",
        price: 89.99,
        originalPrice: 119.99,
        image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        size: "M",
        color: "Black",
        quantity: 1
    },
    {
        id: 2,
        name: "Midnight Essence",
        category: "Perfumes",
        price: 149.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1594035910387-fea47794261f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        size: "50ml",
        color: null,
        quantity: 1
    },
    {
        id: 3,
        name: "Tech Pro Backpack",
        category: "Essentials",
        price: 79.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        size: "One Size",
        color: "Black",
        quantity: 1
    }
];

// Recommended products
const recommendedProducts = [
    {
        id: 4,
        name: "Street King T-Shirt",
        price: 45.99,
        image: "https://images.unsplash.com/photo-1503341504253-dff4815485f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 89
    },
    {
        id: 5,
        name: "Royal Chain Necklace",
        price: 299.99,
        image: "https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 45
    },
    {
        id: 6,
        name: "Urban Fresh Cologne",
        price: 89.99,
        image: "https://images.unsplash.com/photo-1541643600914-78b084683601?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 156
    },
    {
        id: 7,
        name: "Signature Snapback",
        price: 35.99,
        image: "https://images.unsplash.com/photo-1588850561407-ed78c282e89b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 156
    }
];

// DOM Elements
const cartList = document.getElementById('cartList');
const clearCartBtn = document.getElementById('clearCartBtn');
const promoInput = document.getElementById('promoInput');
const promoBtn = document.getElementById('promoBtn');
const promoMessage = document.getElementById('promoMessage');
const checkoutBtn = document.getElementById('checkoutBtn');
const recommendedGrid = document.getElementById('recommendedGrid');

// State
let promoCode = null;
let promoDiscount = 0;

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    renderCart();
    renderRecommended();
    setupEventListeners();
    updateCartSummary();
});

// Event Listeners
function setupEventListeners() {
    // Clear cart
    clearCartBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to clear your cart?')) {
            cartItems = [];
            renderCart();
            updateCartSummary();
            updateCartBadge();
        }
    });

    // Promo code
    promoBtn.addEventListener('click', applyPromoCode);
    promoInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            applyPromoCode();
        }
    });

    // Checkout
    checkoutBtn.addEventListener('click', () => {
        if (cartItems.length === 0) {
            alert('Your cart is empty!');
            return;
        }
        
        // In a real app, this would redirect to checkout page
        alert('Redirecting to secure checkout...\n\nTotal: ' + document.getElementById('total').textContent);
    });
}

// Render cart items
function renderCart() {
    if (cartItems.length === 0) {
        cartList.innerHTML = `
            <div class="empty-cart">
                <div class="empty-cart-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <h3>Your cart is empty</h3>
                <p>Add some items to get started</p>
                <a href="index.html" class="btn btn-primary">Start Shopping</a>
            </div>
        `;
        
        // Update header
        document.querySelector('.cart-header h2').textContent = 'Cart Items (0)';
        return;
    }

    cartList.innerHTML = cartItems.map(item => `
        <div class="cart-item" data-item-id="${item.id}">
            <div class="item-image">
                <img src="${item.image}" alt="${item.name}" loading="lazy">
            </div>
            
            <div class="item-details">
                <h3>${item.name}</h3>
                <p>${item.category}${item.size ? ` • Size: ${item.size}` : ''}${item.color ? ` • Color: ${item.color}` : ''}</p>
                <div class="item-price">
                    $${item.price}
                    ${item.originalPrice ? `<span class="original-price">$${item.originalPrice}</span>` : ''}
                </div>
            </div>
            
            <div class="quantity-controls">
                <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity - 1})" ${item.quantity <= 1 ? 'disabled' : ''}>
                    <i class="fas fa-minus"></i>
                </button>
                <input type="number" class="quantity-input" value="${item.quantity}" min="1" max="10" 
                       onchange="updateQuantity(${item.id}, this.value)">
                <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity + 1})" ${item.quantity >= 10 ? 'disabled' : ''}>
                    <i class="fas fa-plus"></i>
                </button>
            </div>
            
            <div class="item-total">
                $${(item.price * item.quantity).toFixed(2)}
            </div>
            
            <button class="remove-btn" onclick="removeItem(${item.id})" title="Remove item">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `).join('');

    // Update header
    document.querySelector('.cart-header h2').textContent = `Cart Items (${cartItems.length})`;
}

// Render recommended products
function renderRecommended() {
    recommendedGrid.innerHTML = recommendedProducts.map(product => `
        <div class="product-card">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" loading="lazy">
                <div class="product-overlay">
                    <button class="add-to-cart-btn" onclick="addRecommendedToCart(${product.id})">
                        <i class="fas fa-shopping-cart"></i>
                        Add to Cart
                    </button>
                </div>
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <div class="product-price">
                    <span class="current-price">$${product.price}</span>
                </div>
                <div class="product-rating">
                    <div class="stars">
                        ${generateStars(product.rating)}
                    </div>
                    <span class="rating-count">(${product.reviews})</span>
                </div>
            </div>
        </div>
    `).join('');
}

// Update quantity
function updateQuantity(itemId, newQuantity) {
    newQuantity = parseInt(newQuantity);
    
    if (newQuantity < 1 || newQuantity > 10) return;
    
    const item = cartItems.find(item => item.id === itemId);
    if (item) {
        item.quantity = newQuantity;
        renderCart();
        updateCartSummary();
        updateCartBadge();
    }
}

// Remove item
function removeItem(itemId) {
    if (confirm('Remove this item from your cart?')) {
        cartItems = cartItems.filter(item => item.id !== itemId);
        renderCart();
        updateCartSummary();
        updateCartBadge();
    }
}

// Add recommended product to cart
function addRecommendedToCart(productId) {
    const product = recommendedProducts.find(p => p.id === productId);
    
    // Check if item already in cart
    const existingItem = cartItems.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cartItems.push({
            id: product.id,
            name: product.name,
            category: "Recommended",
            price: product.price,
            originalPrice: null,
            image: product.image,
            size: "One Size",
            color: null,
            quantity: 1
        });
    }
    
    renderCart();
    updateCartSummary();
    updateCartBadge();
    
    // Show success message
    alert(`Added "${product.name}" to cart!`);
}

// Apply promo code
function applyPromoCode() {
    const code = promoInput.value.trim().toUpperCase();
    
    // Valid promo codes
    const promoCodes = {
        'SAVE10': { discount: 0.10, message: '10% discount applied!' },
        'WELCOME20': { discount: 0.20, message: '20% welcome discount applied!' },
        'MAGNA5': { discount: 0.15, message: '15% Magna5RRR discount applied!' }
    };
    
    if (promoCodes[code]) {
        promoCode = code;
        promoDiscount = promoCodes[code].discount;
        promoMessage.textContent = promoCodes[code].message;
        promoMessage.className = 'promo-message success';
        promoBtn.textContent = 'Applied';
        promoBtn.disabled = true;
        promoInput.disabled = true;
    } else {
        promoMessage.textContent = 'Invalid promo code';
        promoMessage.className = 'promo-message error';
    }
    
    updateCartSummary();
}

// Update cart summary
function updateCartSummary() {
    const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const shipping = subtotal > 100 ? 0 : 9.99;
    const discount = subtotal * promoDiscount;
    const tax = (subtotal - discount + shipping) * 0.08; // 8% tax
    const total = subtotal - discount + shipping + tax;
    
    document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
    document.getElementById('shipping').textContent = shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`;
    document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
    document.getElementById('total').textContent = `$${total.toFixed(2)}`;
    
    // Show discount if applied
    const summaryCard = document.querySelector('.summary-card');
    let discountRow = summaryCard.querySelector('.discount-row');
    
    if (promoDiscount > 0) {
        if (!discountRow) {
            discountRow = document.createElement('div');
            discountRow.className = 'summary-row discount-row';
            discountRow.style.color = 'var(--primary-600)';
            
            const taxRow = summaryCard.querySelector('.summary-row:nth-last-child(3)');
            taxRow.parentNode.insertBefore(discountRow, taxRow.nextSibling);
        }
        discountRow.innerHTML = `
            <span>Discount (${promoCode})</span>
            <span>-$${discount.toFixed(2)}</span>
        `;
    } else if (discountRow) {
        discountRow.remove();
    }
}

// Update cart badge
function updateCartBadge() {
    const cartBadge = document.querySelector('.cart-btn .badge');
    if (cartBadge) {
        cartBadge.textContent = cartItems.length;
    }
}

// Generate star rating HTML
function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    return stars;
}

// Save cart to localStorage (for persistence)
function saveCart() {
    localStorage.setItem('magna5rrr_cart', JSON.stringify(cartItems));
}

// Load cart from localStorage
function loadCart() {
    const saved = localStorage.getItem('magna5rrr_cart');
    if (saved) {
        cartItems = JSON.parse(saved);
    }
}

// Auto-save cart when it changes
const originalRenderCart = renderCart;
renderCart = function() {
    originalRenderCart();
    saveCart();
};

// Load cart on page load
document.addEventListener('DOMContentLoaded', () => {
    loadCart();
});
