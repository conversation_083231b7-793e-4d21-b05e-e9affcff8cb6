/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    
    --accent-400: #f87171;
    --accent-500: #ef4444;
    --accent-600: #dc2626;
    
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    
    /* Typography */
    --font-primary: 'Inter', system-ui, sans-serif;
    --font-display: 'Playfair Display', serif;
    
    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 5rem 0;
    
    /* Transitions */
    --transition-fast: 0.2s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--neutral-800);
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
    overflow-x: hidden;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--neutral-200);
    transition: var(--transition-normal);
}

.navbar {
    padding: 0 1rem;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

/* Logo */
.nav-logo .logo-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--neutral-900);
}

.logo-icon {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, var(--neutral-900), var(--neutral-700));
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
}

.logo-text {
    font-family: var(--font-display);
    font-weight: bold;
    font-size: 1.25rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--neutral-700);
    font-weight: 500;
    position: relative;
    transition: var(--transition-fast);
}

.nav-link:hover {
    color: var(--neutral-900);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -0.25rem;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--neutral-900);
    transition: var(--transition-normal);
}

.nav-link:hover::after {
    width: 100%;
}

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-icon {
    position: relative;
    padding: 0.5rem;
    color: var(--neutral-700);
    text-decoration: none;
    border: none;
    background: none;
    cursor: pointer;
    transition: var(--transition-fast);
}

.nav-icon:hover {
    color: var(--neutral-900);
}

.badge {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    width: 1rem;
    height: 1rem;
    background: var(--accent-500);
    color: white;
    font-size: 0.75rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Mobile Toggle */
.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 0.25rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.nav-toggle span {
    width: 1.5rem;
    height: 2px;
    background: var(--neutral-700);
    transition: var(--transition-normal);
}

/* Search Bar */
.search-bar {
    display: none;
    padding: 1rem;
    border-top: 1px solid var(--neutral-200);
}

.search-bar.active {
    display: block;
}

.search-container {
    position: relative;
    max-width: var(--container-max-width);
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    font-size: 1rem;
    outline: none;
    transition: var(--transition-fast);
}

.search-input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--neutral-400);
}

/* Mobile Menu */
.mobile-menu {
    display: none;
    flex-direction: column;
    padding: 1rem;
    border-top: 1px solid var(--neutral-200);
    gap: 1rem;
}

.mobile-menu.active {
    display: flex;
}

.mobile-link {
    text-decoration: none;
    color: var(--neutral-700);
    font-weight: 500;
    padding: 0.5rem 0;
    transition: var(--transition-fast);
}

.mobile-link:hover {
    color: var(--neutral-900);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    inset: 0;
    z-index: 0;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
}

.hero-content {
    position: relative;
    z-index: 10;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 1rem;
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-family: var(--font-display);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: bold;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-title .highlight {
    color: var(--primary-400);
}

.hero-subtitle {
    font-size: clamp(1.125rem, 3vw, 1.5rem);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.accent-blue {
    color: var(--primary-400);
    font-weight: 600;
}

.accent-red {
    color: var(--accent-400);
    font-weight: 600;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 1rem;
}

.btn-primary {
    background: var(--neutral-900);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.2);
}

.btn-primary:hover {
    background: var(--neutral-800);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline {
    background: transparent;
    color: var(--neutral-900);
    border: 2px solid var(--neutral-900);
}

.btn-outline:hover {
    background: var(--neutral-900);
    color: white;
}

.hero-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 4rem;
}

/* Hero Stats */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.scroll-mouse {
    width: 1.5rem;
    height: 2.5rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 1rem;
    display: flex;
    justify-content: center;
    padding-top: 0.5rem;
}

.scroll-wheel {
    width: 0.25rem;
    height: 0.75rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0.125rem;
    animation: scroll 2s infinite;
}

/* Floating Elements */
.floating-element {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    animation: float 6s ease-in-out infinite;
}

.floating-1 {
    top: 5rem;
    left: 2.5rem;
    width: 5rem;
    height: 5rem;
    background: rgba(14, 165, 233, 0.2);
    animation-delay: 0s;
}

.floating-2 {
    bottom: 8rem;
    right: 4rem;
    width: 8rem;
    height: 8rem;
    background: rgba(239, 68, 68, 0.2);
    animation-delay: 1s;
}

.floating-3 {
    top: 50%;
    left: 5rem;
    width: 4rem;
    height: 4rem;
    background: rgba(255, 255, 255, 0.1);
    animation-delay: 2s;
}

/* Categories Section */
.categories {
    padding: var(--section-padding);
    background: var(--neutral-50);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: bold;
    color: var(--neutral-900);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--neutral-600);
    max-width: 600px;
    margin: 0 auto;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.category-card {
    position: relative;
    aspect-ratio: 3/4;
    border-radius: 1rem;
    overflow: hidden;
    text-decoration: none;
    color: white;
    transition: var(--transition-slow);
    cursor: pointer;
}

.category-card:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.category-image {
    position: absolute;
    inset: 0;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.category-card:hover .category-image img {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    inset: 0;
    opacity: 0.6;
    transition: var(--transition-normal);
}

.category-card:hover .category-overlay {
    opacity: 0.7;
}

.overlay-dark {
    background: linear-gradient(135deg, var(--neutral-900), var(--neutral-700));
}

.overlay-blue {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
}

.overlay-red {
    background: linear-gradient(135deg, var(--accent-500), var(--accent-700));
}

.overlay-gray {
    background: linear-gradient(135deg, var(--neutral-600), var(--neutral-800));
}

.category-content {
    position: absolute;
    inset: 0;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    z-index: 10;
}

.category-name {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    transition: var(--transition-normal);
}

.category-description {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1rem;
    transition: var(--transition-normal);
}

.category-cta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: var(--transition-normal);
}

.category-card:hover .category-name,
.category-card:hover .category-description,
.category-card:hover .category-cta {
    transform: translateY(-4px);
}

.category-card:hover .category-cta i {
    transform: translateX(4px);
}

.section-cta {
    text-align: center;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scroll {
    0%, 20% {
        transform: translateY(0);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    80%, 100% {
        transform: translateY(0.75rem);
        opacity: 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .floating-element {
        display: none;
    }
}

/* Featured Products */
.featured-products {
    padding: var(--section-padding);
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.product-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: var(--transition-normal);
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.quick-view-btn,
.wishlist-btn-product {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    background: white;
    color: var(--neutral-900);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.wishlist-btn-product {
    padding: 0.75rem;
    width: auto;
    aspect-ratio: 1;
}

.quick-view-btn:hover,
.wishlist-btn-product:hover {
    background: var(--neutral-100);
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    z-index: 5;
}

.product-badge.new {
    background: var(--primary-500);
    color: white;
}

.product-badge.bestseller {
    background: var(--accent-500);
    color: white;
}

.product-badge.limited {
    background: var(--neutral-900);
    color: white;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.25rem;
}

.product-category {
    color: var(--neutral-500);
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.current-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--neutral-900);
}

.original-price {
    font-size: 1rem;
    color: var(--neutral-400);
    text-decoration: line-through;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    color: #fbbf24;
}

.rating-count {
    color: var(--neutral-500);
    font-size: 0.875rem;
}

/* Brand Story */
.brand-story {
    padding: var(--section-padding);
    background: var(--neutral-100);
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.story-title {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--neutral-900);
    margin-bottom: 1.5rem;
}

.story-description {
    font-size: 1.125rem;
    color: var(--neutral-600);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.story-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.feature i {
    color: var(--primary-500);
    font-size: 1.25rem;
}

.feature span {
    font-weight: 500;
    color: var(--neutral-700);
}

.story-image {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.story-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Newsletter */
.newsletter {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--neutral-900), var(--neutral-700));
    color: white;
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-title {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.newsletter-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    outline: none;
}

.newsletter-privacy {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Footer */
.footer {
    background: var(--neutral-900);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-link:hover {
    background: var(--primary-500);
    transform: translateY(-2px);
}

.footer-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
}

.payment-methods {
    display: flex;
    gap: 1rem;
    font-size: 1.5rem;
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .categories-grid,
    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .story-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .story-features {
        grid-template-columns: 1fr;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .floating-element {
        display: none;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 0.5rem;
    }

    .hero-content {
        padding: 0 0.5rem;
    }

    .container {
        padding: 0 0.5rem;
    }

    .story-title,
    .newsletter-title {
        font-size: 2rem;
    }
}
