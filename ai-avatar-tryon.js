// AI Avatar Try-On System
class AIAvatarTryOn {
    constructor() {
        this.isActive = false;
        this.currentAvatar = null;
        this.currentProduct = null;
        this.avatarModels = {
            male: {
                id: 'male_model',
                name: 'AL<PERSON>',
                height: '6\'1"',
                build: 'Athletic',
                skinTone: 'medium',
                measurements: {
                    chest: '40"',
                    waist: '32"',
                    shoulders: '18"'
                }
            },
            female: {
                id: 'female_model',
                name: '<PERSON><PERSON><PERSON>',
                height: '5\'8"',
                build: 'Slim',
                skinTone: 'medium',
                measurements: {
                    bust: '34"',
                    waist: '26"',
                    hips: '36"'
                }
            },
            unisex: {
                id: 'unisex_model',
                name: 'JORDA<PERSON>',
                height: '5\'10"',
                build: 'Medium',
                skinTone: 'medium',
                measurements: {
                    chest: '38"',
                    waist: '30"',
                    shoulders: '17"'
                }
            }
        };
        
        this.init();
    }

    init() {
        this.createAvatarInterface();
        this.setupEventListeners();
        this.loadAvatarAssets();
    }

    createAvatarInterface() {
        const avatarHTML = `
            <div class="ai-avatar-system" id="aiAvatarSystem">
                <div class="avatar-toggle" id="avatarToggle">
                    <div class="avatar-icon">
                        <i class="fas fa-user-astronaut"></i>
                    </div>
                    <span class="avatar-label">TRY ON</span>
                </div>

                <div class="avatar-panel" id="avatarPanel">
                    <div class="avatar-header">
                        <h3>AI VIRTUAL TRY-ON</h3>
                        <p>Experience products on realistic avatars</p>
                        <button class="close-avatar-btn" id="closeAvatarBtn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="avatar-content">
                        <!-- Avatar Selection -->
                        <div class="avatar-selection">
                            <h4>SELECT MODEL</h4>
                            <div class="avatar-models">
                                <button class="avatar-model-btn active" data-model="unisex">
                                    <div class="model-preview">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="model-info">
                                        <span class="model-name">JORDAN</span>
                                        <span class="model-details">5'10" • UNISEX</span>
                                    </div>
                                </button>
                                <button class="avatar-model-btn" data-model="male">
                                    <div class="model-preview">
                                        <i class="fas fa-male"></i>
                                    </div>
                                    <div class="model-info">
                                        <span class="model-name">ALEX</span>
                                        <span class="model-details">6'1" • MALE</span>
                                    </div>
                                </button>
                                <button class="avatar-model-btn" data-model="female">
                                    <div class="model-preview">
                                        <i class="fas fa-female"></i>
                                    </div>
                                    <div class="model-info">
                                        <span class="model-name">MAYA</span>
                                        <span class="model-details">5'8" • FEMALE</span>
                                    </div>
                                </button>
                            </div>
                        </div>

                        <!-- Avatar Viewer -->
                        <div class="avatar-viewer" id="avatarViewer">
                            <div class="avatar-canvas">
                                <div class="avatar-model" id="avatarModel">
                                    <div class="model-base">
                                        <div class="model-head"></div>
                                        <div class="model-torso"></div>
                                        <div class="model-arms">
                                            <div class="model-arm left"></div>
                                            <div class="model-arm right"></div>
                                        </div>
                                        <div class="model-legs">
                                            <div class="model-leg left"></div>
                                            <div class="model-leg right"></div>
                                        </div>
                                    </div>
                                    <div class="product-overlay" id="productOverlay">
                                        <!-- Product will be overlaid here -->
                                    </div>
                                </div>
                            </div>
                            
                            <div class="avatar-controls">
                                <button class="avatar-control-btn" onclick="aiAvatarTryOn.rotateAvatar('left')">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button class="avatar-control-btn" onclick="aiAvatarTryOn.resetView()">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="avatar-control-btn" onclick="aiAvatarTryOn.rotateAvatar('right')">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Product Info -->
                        <div class="tryon-product-info" id="tryonProductInfo">
                            <div class="no-product-selected">
                                <i class="fas fa-tshirt"></i>
                                <p>Select a product to try on</p>
                            </div>
                        </div>

                        <!-- Size Recommendations -->
                        <div class="size-recommendations" id="sizeRecommendations">
                            <h4>AI SIZE RECOMMENDATION</h4>
                            <div class="size-analysis">
                                <div class="recommended-size">
                                    <span class="size-label">RECOMMENDED</span>
                                    <span class="size-value" id="recommendedSize">-</span>
                                </div>
                                <div class="fit-analysis">
                                    <span class="fit-label">FIT ANALYSIS</span>
                                    <span class="fit-description" id="fitDescription">Select a product first</span>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="tryon-actions">
                            <button class="tryon-btn primary" onclick="aiAvatarTryOn.addToCart()">
                                ADD TO CART
                            </button>
                            <button class="tryon-btn secondary" onclick="aiAvatarTryOn.shareOutfit()">
                                SHARE LOOK
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', avatarHTML);
        this.addAvatarStyles();
    }

    setupEventListeners() {
        const avatarToggle = document.getElementById('avatarToggle');
        const avatarPanel = document.getElementById('avatarPanel');
        const closeBtn = document.getElementById('closeAvatarBtn');
        
        avatarToggle.addEventListener('click', () => {
            this.toggleAvatarPanel();
        });

        closeBtn.addEventListener('click', () => {
            this.closeAvatarPanel();
        });

        // Model selection
        document.querySelectorAll('.avatar-model-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const model = e.currentTarget.dataset.model;
                this.selectAvatarModel(model);
            });
        });

        // Product try-on from product cards
        document.addEventListener('click', (e) => {
            if (e.target.closest('.try-on-btn')) {
                const productCard = e.target.closest('.product-card');
                this.tryOnProduct(productCard);
            }
        });
    }

    toggleAvatarPanel() {
        const panel = document.getElementById('avatarPanel');
        this.isActive = !this.isActive;
        panel.classList.toggle('active', this.isActive);
        
        if (this.isActive) {
            this.initializeAvatar();
        }
    }

    closeAvatarPanel() {
        const panel = document.getElementById('avatarPanel');
        this.isActive = false;
        panel.classList.remove('active');
    }

    selectAvatarModel(modelType) {
        this.currentAvatar = this.avatarModels[modelType];
        
        // Update active button
        document.querySelectorAll('.avatar-model-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-model="${modelType}"]`).classList.add('active');
        
        // Update avatar display
        this.updateAvatarDisplay();
        
        // Recalculate size recommendations
        if (this.currentProduct) {
            this.calculateSizeRecommendation();
        }
    }

    updateAvatarDisplay() {
        const avatarModel = document.getElementById('avatarModel');
        if (!avatarModel || !this.currentAvatar) return;

        // Update avatar appearance based on selected model
        avatarModel.className = `avatar-model ${this.currentAvatar.id}`;
        avatarModel.dataset.model = this.currentAvatar.id;
    }

    tryOnProduct(productCard) {
        if (!productCard) return;

        const productName = productCard.querySelector('.product-name')?.textContent;
        const productCategory = productCard.querySelector('.product-category')?.textContent;
        const productImage = productCard.querySelector('img')?.src;
        const productPrice = productCard.querySelector('.current-price')?.textContent;

        this.currentProduct = {
            name: productName,
            category: productCategory,
            image: productImage,
            price: productPrice,
            type: this.getProductType(productCategory)
        };

        // Open avatar panel if not already open
        if (!this.isActive) {
            this.toggleAvatarPanel();
        }

        // Apply product to avatar
        this.applyProductToAvatar();
        
        // Update product info
        this.updateProductInfo();
        
        // Calculate size recommendation
        this.calculateSizeRecommendation();
    }

    getProductType(category) {
        const categoryLower = category?.toLowerCase() || '';
        
        if (categoryLower.includes('hoodie') || categoryLower.includes('sweatshirt')) return 'hoodie';
        if (categoryLower.includes('tee') || categoryLower.includes('shirt')) return 'tshirt';
        if (categoryLower.includes('jacket') || categoryLower.includes('bomber')) return 'jacket';
        if (categoryLower.includes('tracksuit') || categoryLower.includes('joggers')) return 'tracksuit';
        if (categoryLower.includes('jewelry') || categoryLower.includes('necklace')) return 'jewelry';
        if (categoryLower.includes('perfume') || categoryLower.includes('fragrance')) return 'fragrance';
        
        return 'clothing';
    }

    applyProductToAvatar() {
        const productOverlay = document.getElementById('productOverlay');
        if (!productOverlay || !this.currentProduct) return;

        // Create product visualization based on type
        const productElement = this.createProductVisualization();
        productOverlay.innerHTML = '';
        productOverlay.appendChild(productElement);
    }

    createProductVisualization() {
        const productDiv = document.createElement('div');
        productDiv.className = `product-visualization ${this.currentProduct.type}`;
        
        switch (this.currentProduct.type) {
            case 'hoodie':
                productDiv.innerHTML = `
                    <div class="hoodie-overlay">
                        <div class="hoodie-body"></div>
                        <div class="hoodie-hood"></div>
                        <div class="hoodie-sleeves">
                            <div class="sleeve left"></div>
                            <div class="sleeve right"></div>
                        </div>
                    </div>
                `;
                break;
            case 'tshirt':
                productDiv.innerHTML = `
                    <div class="tshirt-overlay">
                        <div class="tshirt-body"></div>
                        <div class="tshirt-sleeves">
                            <div class="sleeve left"></div>
                            <div class="sleeve right"></div>
                        </div>
                    </div>
                `;
                break;
            case 'jewelry':
                productDiv.innerHTML = `
                    <div class="jewelry-overlay">
                        <div class="necklace"></div>
                    </div>
                `;
                break;
            default:
                productDiv.innerHTML = `
                    <div class="generic-overlay">
                        <div class="product-indicator">
                            <i class="fas fa-tshirt"></i>
                        </div>
                    </div>
                `;
        }
        
        return productDiv;
    }

    updateProductInfo() {
        const productInfo = document.getElementById('tryonProductInfo');
        if (!productInfo || !this.currentProduct) return;

        productInfo.innerHTML = `
            <div class="selected-product">
                <div class="product-image">
                    <img src="${this.currentProduct.image}" alt="${this.currentProduct.name}">
                </div>
                <div class="product-details">
                    <h5>${this.currentProduct.name}</h5>
                    <p>${this.currentProduct.category}</p>
                    <span class="price">${this.currentProduct.price}</span>
                </div>
            </div>
        `;
    }

    calculateSizeRecommendation() {
        if (!this.currentAvatar || !this.currentProduct) return;

        // AI-powered size recommendation logic
        const sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
        const recommendedSize = this.getRecommendedSize();
        const fitDescription = this.getFitDescription(recommendedSize);

        document.getElementById('recommendedSize').textContent = recommendedSize;
        document.getElementById('fitDescription').textContent = fitDescription;
    }

    getRecommendedSize() {
        // Simplified AI logic - in real implementation, this would use ML models
        const avatarBuild = this.currentAvatar.build.toLowerCase();
        const productType = this.currentProduct.type;

        if (avatarBuild === 'slim') return 'S';
        if (avatarBuild === 'athletic') return 'M';
        if (avatarBuild === 'medium') return 'M';
        
        return 'L';
    }

    getFitDescription(size) {
        const descriptions = {
            'XS': 'Tight fit, perfect for layering',
            'S': 'Slim fit, modern silhouette',
            'M': 'Regular fit, comfortable wear',
            'L': 'Relaxed fit, street style',
            'XL': 'Oversized fit, trendy look',
            'XXL': 'Extra oversized, statement piece'
        };
        
        return descriptions[size] || 'Perfect fit for your style';
    }

    rotateAvatar(direction) {
        const avatarModel = document.getElementById('avatarModel');
        if (!avatarModel) return;

        const currentRotation = parseInt(avatarModel.dataset.rotation || '0');
        const newRotation = direction === 'left' ? currentRotation - 90 : currentRotation + 90;
        
        avatarModel.style.transform = `rotateY(${newRotation}deg)`;
        avatarModel.dataset.rotation = newRotation.toString();
    }

    resetView() {
        const avatarModel = document.getElementById('avatarModel');
        if (!avatarModel) return;

        avatarModel.style.transform = 'rotateY(0deg)';
        avatarModel.dataset.rotation = '0';
    }

    addToCart() {
        if (!this.currentProduct) return;
        
        // Add product to cart with recommended size
        const recommendedSize = document.getElementById('recommendedSize').textContent;
        console.log(`Adding ${this.currentProduct.name} (Size: ${recommendedSize}) to cart`);
        
        // Show success message
        this.showMessage('Added to cart!', 'success');
    }

    shareOutfit() {
        if (!this.currentProduct) return;
        
        // Generate shareable outfit link
        const outfitData = {
            product: this.currentProduct.name,
            avatar: this.currentAvatar.name,
            size: document.getElementById('recommendedSize').textContent
        };
        
        console.log('Sharing outfit:', outfitData);
        this.showMessage('Outfit shared!', 'success');
    }

    showMessage(text, type = 'info') {
        const message = document.createElement('div');
        message.className = `tryon-message ${type}`;
        message.textContent = text;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 3000);
    }

    initializeAvatar() {
        this.currentAvatar = this.avatarModels.unisex;
        this.updateAvatarDisplay();
    }

    loadAvatarAssets() {
        // Preload avatar assets and 3D models
        console.log('Loading AI Avatar assets...');
    }

    addAvatarStyles() {
        // Avatar styles will be added via CSS file
        console.log('Avatar styles loaded');
    }
}

// Initialize AI Avatar Try-On system
document.addEventListener('DOMContentLoaded', () => {
    window.aiAvatarTryOn = new AIAvatarTryOn();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIAvatarTryOn;
}
