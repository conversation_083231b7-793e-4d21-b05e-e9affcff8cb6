// Futuristic Theme Controller
class ThemeController {
    constructor() {
        this.currentTheme = localStorage.getItem('magna-theme') || 'light';
        this.currentMood = localStorage.getItem('magna-mood') || 'energetic';
        this.isAutoMode = localStorage.getItem('magna-auto-mode') === 'true';
        
        this.init();
        this.createParticles();
        this.setupAutoMode();
        this.setupScrollAnimations();
    }

    init() {
        this.createThemeToggle();
        this.applyTheme(this.currentTheme);
        this.applyMood(this.currentMood);
        this.setupEventListeners();
        this.detectUserPreferences();
    }

    createThemeToggle() {
        const themeToggle = document.createElement('div');
        themeToggle.className = 'theme-toggle';
        themeToggle.innerHTML = `
            <div class="theme-controls">
                <div class="theme-section">
                    <div class="theme-section-title">MODE</div>
                    <button class="theme-btn light-mode-btn" data-theme="light" title="Day Mode">
                        <i class="fas fa-sun"></i>
                    </button>
                    <button class="theme-btn dark-mode-btn" data-theme="dark" title="Night Mode">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="theme-btn auto-mode-btn" data-theme="auto" title="Auto Mode">
                        <i class="fas fa-clock"></i>
                    </button>
                </div>

                <div class="theme-section">
                    <div class="theme-section-title">VIBE</div>
                    <div class="mood-controls">
                        <button class="mood-btn mood-energetic" data-mood="energetic" title="Energetic"></button>
                        <button class="mood-btn mood-calm" data-mood="calm" title="Calm"></button>
                        <button class="mood-btn mood-mysterious" data-mood="mysterious" title="Mysterious"></button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(themeToggle);
    }

    setupEventListeners() {
        // Theme buttons
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const theme = e.currentTarget.dataset.theme;
                if (theme === 'auto') {
                    this.toggleAutoMode();
                } else {
                    this.setTheme(theme);
                }
            });
        });

        // Mood buttons
        document.querySelectorAll('.mood-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const mood = e.currentTarget.dataset.mood;
                this.setMood(mood);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'd':
                        e.preventDefault();
                        this.toggleTheme();
                        break;
                    case 'm':
                        e.preventDefault();
                        this.cycleMood();
                        break;
                    case 'a':
                        e.preventDefault();
                        this.toggleAutoMode();
                        break;
                }
            }
        });
    }

    setTheme(theme) {
        this.currentTheme = theme;
        this.isAutoMode = false;
        this.applyTheme(theme);
        this.updateActiveButtons();
        localStorage.setItem('magna-theme', theme);
        localStorage.setItem('magna-auto-mode', 'false');
        
        // Trigger theme change event
        this.dispatchThemeEvent('themeChanged', { theme, mood: this.currentMood });
    }

    setMood(mood) {
        this.currentMood = mood;
        this.applyMood(mood);
        this.updateActiveMoodButtons();
        localStorage.setItem('magna-mood', mood);
        
        // Update particles based on mood
        this.updateParticlesMood(mood);
        
        // Trigger mood change event
        this.dispatchThemeEvent('moodChanged', { theme: this.currentTheme, mood });
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        // Update meta theme color
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        
        metaThemeColor.content = theme === 'dark' ? '#0f0f23' : '#f8fafc';
        
        // Add theme transition class
        document.body.classList.add('theme-transitioning');
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
        }, 500);
    }

    applyMood(mood) {
        document.documentElement.setAttribute('data-mood', mood);
        
        // Update background based on mood
        this.updateMoodBackground(mood);
    }

    updateMoodBackground(mood) {
        const moodGradients = {
            energetic: 'radial-gradient(circle at 20% 80%, rgba(255, 0, 128, 0.15) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 0, 0.1) 0%, transparent 50%)',
            calm: 'radial-gradient(circle at 30% 70%, rgba(78, 205, 196, 0.1) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(69, 183, 209, 0.1) 0%, transparent 50%)',
            mysterious: 'radial-gradient(circle at 40% 60%, rgba(191, 0, 255, 0.15) 0%, transparent 50%), radial-gradient(circle at 60% 40%, rgba(128, 0, 255, 0.1) 0%, transparent 50%)'
        };

        const existingOverlay = document.querySelector('.mood-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        const moodOverlay = document.createElement('div');
        moodOverlay.className = 'mood-overlay';
        moodOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: ${moodGradients[mood]};
            pointer-events: none;
            z-index: -1;
            transition: opacity 1s ease;
            opacity: 0;
        `;
        
        document.body.appendChild(moodOverlay);
        
        // Fade in the new mood overlay
        requestAnimationFrame(() => {
            moodOverlay.style.opacity = '1';
        });
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    cycleMood() {
        const moods = ['energetic', 'calm', 'mysterious'];
        const currentIndex = moods.indexOf(this.currentMood);
        const nextMood = moods[(currentIndex + 1) % moods.length];
        this.setMood(nextMood);
    }

    toggleAutoMode() {
        this.isAutoMode = !this.isAutoMode;
        localStorage.setItem('magna-auto-mode', this.isAutoMode.toString());
        
        if (this.isAutoMode) {
            this.startAutoMode();
        } else {
            this.stopAutoMode();
        }
        
        this.updateActiveButtons();
    }

    startAutoMode() {
        // Auto theme based on time
        this.autoThemeInterval = setInterval(() => {
            const hour = new Date().getHours();
            const autoTheme = (hour >= 6 && hour < 18) ? 'light' : 'dark';
            if (autoTheme !== this.currentTheme) {
                this.applyTheme(autoTheme);
                this.currentTheme = autoTheme;
            }
        }, 60000); // Check every minute

        // Auto mood based on user activity
        this.setupActivityBasedMood();
    }

    stopAutoMode() {
        if (this.autoThemeInterval) {
            clearInterval(this.autoThemeInterval);
        }
        if (this.activityMoodInterval) {
            clearInterval(this.activityMoodInterval);
        }
    }

    setupActivityBasedMood() {
        let scrollActivity = 0;
        let clickActivity = 0;
        
        const trackActivity = () => {
            document.addEventListener('scroll', () => scrollActivity++);
            document.addEventListener('click', () => clickActivity++);
        };
        
        trackActivity();
        
        this.activityMoodInterval = setInterval(() => {
            const totalActivity = scrollActivity + clickActivity;
            
            let newMood;
            if (totalActivity > 20) {
                newMood = 'energetic';
            } else if (totalActivity > 5) {
                newMood = 'calm';
            } else {
                newMood = 'mysterious';
            }
            
            if (newMood !== this.currentMood) {
                this.setMood(newMood);
            }
            
            // Reset counters
            scrollActivity = 0;
            clickActivity = 0;
        }, 30000); // Check every 30 seconds
    }

    updateActiveButtons() {
        // Update theme buttons
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        if (this.isAutoMode) {
            document.querySelector('.auto-mode-btn').classList.add('active');
        } else {
            document.querySelector(`[data-theme="${this.currentTheme}"]`).classList.add('active');
        }
    }

    updateActiveMoodButtons() {
        document.querySelectorAll('.mood-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-mood="${this.currentMood}"]`).classList.add('active');
    }

    createParticles() {
        const particlesContainer = document.createElement('div');
        particlesContainer.className = 'cyber-particles';
        
        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
            particlesContainer.appendChild(particle);
        }
        
        document.body.appendChild(particlesContainer);
    }

    updateParticlesMood(mood) {
        const particles = document.querySelectorAll('.particle');
        const moodColors = {
            energetic: ['#ff0080', '#ffff00', '#00f5ff'],
            calm: ['#4ecdc4', '#45b7d1', '#96ceb4'],
            mysterious: ['#bf00ff', '#8000ff', '#4000ff']
        };
        
        particles.forEach((particle, index) => {
            const colors = moodColors[mood];
            const color = colors[index % colors.length];
            particle.style.background = color;
            particle.style.boxShadow = `0 0 20px ${color}`;
        });
    }

    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Add floating animation to product cards
                    if (entry.target.classList.contains('product-card')) {
                        entry.target.classList.add('floating-product');
                    }
                }
            });
        }, observerOptions);

        // Observe all cards and sections
        document.querySelectorAll('.cyber-card, .product-card, section').forEach(el => {
            observer.observe(el);
        });
    }

    detectUserPreferences() {
        // Detect system theme preference
        if (window.matchMedia && !localStorage.getItem('magna-theme')) {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            this.setTheme(prefersDark ? 'dark' : 'light');
        }

        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (this.isAutoMode) {
                    this.setTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }

    dispatchThemeEvent(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    // Public API methods
    getCurrentTheme() {
        return this.currentTheme;
    }

    getCurrentMood() {
        return this.currentMood;
    }

    isAutoModeEnabled() {
        return this.isAutoMode;
    }
}

// Initialize theme controller when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.themeController = new ThemeController();
    
    // Add CSS for animations
    const animationCSS = `
        .animate-in {
            animation: slideInUp 0.6s ease-out forwards;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .theme-transitioning * {
            transition: background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease !important;
        }
    `;
    
    const style = document.createElement('style');
    style.textContent = animationCSS;
    document.head.appendChild(style);
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeController;
}
