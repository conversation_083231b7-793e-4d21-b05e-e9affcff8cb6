// AI Assistant with User Activity Tracking
class MagnaAIAssistant {
    constructor() {
        this.isActive = false;
        this.userSession = this.initializeSession();
        this.conversationHistory = [];
        this.userActivity = [];
        this.knowledgeBase = this.initializeKnowledgeBase();
        this.setupEventListeners();
        this.trackUserActivity();
    }

    initializeSession() {
        const sessionId = localStorage.getItem('magna_session_id') || this.generateSessionId();
        localStorage.setItem('magna_session_id', sessionId);
        
        return {
            id: sessionId,
            startTime: new Date(),
            pageViews: parseInt(localStorage.getItem('magna_page_views') || '0'),
            cartItems: JSON.parse(localStorage.getItem('magna5rrr_cart') || '[]'),
            preferences: JSON.parse(localStorage.getItem('magna_preferences') || '{}')
        };
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    initializeKnowledgeBase() {
        return {
            products: {
                clothing: ['hoodies', 't-shirts', 'jackets', 'pants', 'accessories'],
                perfumes: ['eau de parfum', 'eau de toilette', 'cologne', 'perfume oil'],
                jewelry: ['necklaces', 'rings', 'bracelets', 'earrings'],
                essentials: ['bags', 'tech accessories', 'lifestyle', 'travel']
            },
            shipping: {
                standard: '5-7 business days',
                express: '2-3 business days',
                overnight: 'Next business day',
                international: '10-15 business days'
            },
            returns: {
                period: '30 days',
                condition: 'unworn with tags',
                process: 'online return portal'
            },
            sizes: {
                clothing: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
                perfumes: ['30ml', '50ml', '100ml', '150ml']
            },
            promoCodes: ['SAVE10', 'WELCOME20', 'MAGNA5'],
            faqs: [
                {
                    question: 'How do I track my order?',
                    answer: 'You can track your order using the tracking number sent to your email, or by logging into your account and viewing order history.'
                },
                {
                    question: 'What is your return policy?',
                    answer: 'We offer 30-day returns for unworn items with original tags. Visit our returns page for detailed instructions.'
                },
                {
                    question: 'Do you ship internationally?',
                    answer: 'Yes, we ship to over 25 countries worldwide. International shipping takes 10-15 business days.'
                }
            ]
        };
    }

    setupEventListeners() {
        const chatTrigger = document.getElementById('chatTrigger');
        const chatToggle = document.getElementById('chatToggle');
        const chatSend = document.getElementById('chatSend');
        const chatInput = document.getElementById('chatInput');

        if (chatTrigger) {
            chatTrigger.addEventListener('click', () => this.toggleChat());
        }

        if (chatToggle) {
            chatToggle.addEventListener('click', () => this.toggleChat());
        }

        if (chatSend) {
            chatSend.addEventListener('click', () => this.sendMessage());
        }

        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendMessage();
                }
            });
        }

        // Quick action buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-action')) {
                const action = e.target.dataset.action;
                this.handleQuickAction(action);
            }
        });
    }

    trackUserActivity() {
        // Track page views
        this.userSession.pageViews++;
        localStorage.setItem('magna_page_views', this.userSession.pageViews.toString());

        // Track time on page
        this.startTime = Date.now();
        
        // Track scroll behavior
        let maxScroll = 0;
        window.addEventListener('scroll', () => {
            const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
            maxScroll = Math.max(maxScroll, scrollPercent);
        });

        // Track clicks and interactions
        document.addEventListener('click', (e) => {
            this.logActivity('click', {
                element: e.target.tagName,
                className: e.target.className,
                text: e.target.textContent?.substring(0, 50)
            });
        });

        // Track when user leaves page
        window.addEventListener('beforeunload', () => {
            this.logActivity('page_exit', {
                timeOnPage: Date.now() - this.startTime,
                maxScroll: maxScroll
            });
        });

        // Show proactive help based on behavior
        setTimeout(() => this.checkForProactiveHelp(), 30000); // After 30 seconds
    }

    logActivity(action, data) {
        const activity = {
            timestamp: new Date().toISOString(),
            action: action,
            page: window.location.pathname,
            data: data
        };

        this.userActivity.push(activity);
        
        // Keep only last 50 activities
        if (this.userActivity.length > 50) {
            this.userActivity = this.userActivity.slice(-50);
        }

        // Store in localStorage for persistence
        localStorage.setItem('magna_user_activity', JSON.stringify(this.userActivity));
    }

    checkForProactiveHelp() {
        const currentPage = window.location.pathname;
        
        // Show help based on user behavior
        if (currentPage.includes('cart.html') && this.userSession.cartItems.length === 0) {
            this.showProactiveMessage("I notice your cart is empty. Would you like me to help you find some great products?");
        } else if (this.userSession.pageViews > 5 && !this.hasInteractedWithChat()) {
            this.showProactiveMessage("Hi! I'm here to help if you have any questions about our products or services.");
        }
    }

    hasInteractedWithChat() {
        return this.conversationHistory.length > 0;
    }

    showProactiveMessage(message) {
        const notification = document.getElementById('chatNotification');
        if (notification) {
            notification.style.display = 'flex';
            notification.textContent = '1';
        }

        // Add message to chat when opened
        this.addBotMessage(message);
    }

    toggleChat() {
        const chatWidget = document.getElementById('chatWidget');
        const chatTrigger = document.getElementById('chatTrigger');
        
        if (chatWidget && chatTrigger) {
            this.isActive = !this.isActive;
            
            if (this.isActive) {
                chatWidget.classList.add('active');
                chatTrigger.style.display = 'none';
                this.hideNotification();
            } else {
                chatWidget.classList.remove('active');
                chatTrigger.style.display = 'flex';
            }
        }
    }

    hideNotification() {
        const notification = document.getElementById('chatNotification');
        if (notification) {
            notification.style.display = 'none';
        }
    }

    sendMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();
        
        if (message) {
            this.addUserMessage(message);
            chatInput.value = '';
            
            // Process message and respond
            setTimeout(() => {
                const response = this.processMessage(message);
                this.addBotMessage(response);
            }, 1000);
        }
    }

    addUserMessage(message) {
        const chatMessages = document.getElementById('chatMessages');
        const messageElement = document.createElement('div');
        messageElement.className = 'message user-message';
        messageElement.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="message-content">
                <p>${message}</p>
            </div>
        `;
        
        chatMessages.appendChild(messageElement);
        this.scrollToBottom();
        
        // Log conversation
        this.conversationHistory.push({
            type: 'user',
            message: message,
            timestamp: new Date().toISOString()
        });
    }

    addBotMessage(message, includeActions = false) {
        const chatMessages = document.getElementById('chatMessages');
        const messageElement = document.createElement('div');
        messageElement.className = 'message bot-message';
        
        let actionsHtml = '';
        if (includeActions) {
            actionsHtml = `
                <div class="quick-actions">
                    <button class="quick-action" data-action="track-order">Track Order</button>
                    <button class="quick-action" data-action="size-guide">Size Guide</button>
                    <button class="quick-action" data-action="returns">Returns</button>
                </div>
            `;
        }
        
        messageElement.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <p>${message}</p>
                ${actionsHtml}
            </div>
        `;
        
        chatMessages.appendChild(messageElement);
        this.scrollToBottom();
        
        // Log conversation
        this.conversationHistory.push({
            type: 'bot',
            message: message,
            timestamp: new Date().toISOString()
        });
    }

    processMessage(message) {
        const lowerMessage = message.toLowerCase();
        
        // Intent recognition
        if (lowerMessage.includes('track') || lowerMessage.includes('order')) {
            return this.handleOrderTracking();
        } else if (lowerMessage.includes('size') || lowerMessage.includes('fit')) {
            return this.handleSizeGuide();
        } else if (lowerMessage.includes('return') || lowerMessage.includes('exchange')) {
            return this.handleReturns();
        } else if (lowerMessage.includes('shipping') || lowerMessage.includes('delivery')) {
            return this.handleShipping();
        } else if (lowerMessage.includes('payment') || lowerMessage.includes('pay')) {
            return this.handlePayment();
        } else if (lowerMessage.includes('promo') || lowerMessage.includes('discount') || lowerMessage.includes('code')) {
            return this.handlePromoCodes();
        } else if (lowerMessage.includes('product') || lowerMessage.includes('recommend')) {
            return this.handleProductRecommendations();
        } else if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
            return this.handleGreeting();
        } else {
            return this.handleGeneral(message);
        }
    }

    handleQuickAction(action) {
        switch (action) {
            case 'track-order':
                this.addBotMessage(this.handleOrderTracking());
                break;
            case 'size-guide':
                this.addBotMessage(this.handleSizeGuide());
                break;
            case 'returns':
                this.addBotMessage(this.handleReturns());
                break;
        }
    }

    handleOrderTracking() {
        return "To track your order, please provide your order number (format: M5-2024-XXXXXX) or check your email for the tracking link. You can also view all your orders in your account dashboard.";
    }

    handleSizeGuide() {
        return "Our size guide varies by category:<br>• Clothing: XS-XXL (check our detailed size chart)<br>• Perfumes: 30ml, 50ml, 100ml, 150ml<br>Would you like me to help you find the right size for a specific item?";
    }

    handleReturns() {
        return `Our return policy allows returns within ${this.knowledgeBase.returns.period} for ${this.knowledgeBase.returns.condition}. You can start a return through our ${this.knowledgeBase.returns.process}. Would you like me to guide you through the process?`;
    }

    handleShipping() {
        const shipping = this.knowledgeBase.shipping;
        return `We offer several shipping options:<br>• Standard: ${shipping.standard}<br>• Express: ${shipping.express}<br>• Overnight: ${shipping.overnight}<br>• International: ${shipping.international}<br>Free shipping on orders over $100!`;
    }

    handlePayment() {
        return "We accept all major credit cards, PayPal, Apple Pay, Google Pay, and mobile money options including M-Pesa, MTN Mobile Money, and Airtel Money. All payments are processed securely with SSL encryption.";
    }

    handlePromoCodes() {
        return `Here are some current promo codes you can use:<br>• SAVE10 - 10% off your order<br>• WELCOME20 - 20% off for new customers<br>• MAGNA5 - 15% off Magna5RRR items<br>Enter these at checkout to save!`;
    }

    handleProductRecommendations() {
        const cartItems = this.userSession.cartItems;
        if (cartItems.length > 0) {
            return "Based on your cart, I'd recommend checking out our matching accessories or complementary fragrances. Would you like specific suggestions?";
        } else {
            return "I'd be happy to recommend products! What are you looking for today? Streetwear, fragrances, jewelry, or lifestyle essentials?";
        }
    }

    handleGreeting() {
        const timeOfDay = new Date().getHours() < 12 ? 'morning' : new Date().getHours() < 18 ? 'afternoon' : 'evening';
        return `Good ${timeOfDay}! Welcome to Magna5RRR. I'm your AI assistant and I'm here to help you with any questions about our products, orders, or services. How can I assist you today?`;
    }

    handleGeneral(message) {
        // Search FAQs for relevant answers
        const relevantFaq = this.knowledgeBase.faqs.find(faq => 
            message.toLowerCase().includes(faq.question.toLowerCase().split(' ')[0])
        );
        
        if (relevantFaq) {
            return relevantFaq.answer;
        }
        
        return "I'd be happy to help you with that! For specific questions, you can also contact our human support <NAME_EMAIL> or call +****************. Is there anything specific about our products or services I can help you with?";
    }

    scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }
}

// Initialize AI Assistant when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.magnaAI = new MagnaAIAssistant();
});
