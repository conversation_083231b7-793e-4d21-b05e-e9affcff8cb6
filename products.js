// Product Data
const clothingProducts = [
    {
        id: 1,
        name: "Urban Legend Hoodie",
        category: "hoodies",
        price: 89.99,
        originalPrice: 119.99,
        image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 127,
        colors: ["black", "white", "gray"],
        sizes: ["S", "M", "L", "XL"],
        badge: "new",
        inStock: true
    },
    {
        id: 2,
        name: "Street King T-Shirt",
        category: "t-shirts",
        price: 45.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1503341504253-dff4815485f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 89,
        colors: ["black", "white", "red", "blue"],
        sizes: ["XS", "S", "M", "L", "XL", "XXL"],
        badge: "bestseller",
        inStock: true
    },
    {
        id: 3,
        name: "Rebel Denim Jacket",
        category: "jackets",
        price: 159.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 45,
        colors: ["blue", "black"],
        sizes: ["S", "M", "L", "XL"],
        badge: "limited",
        inStock: true
    },
    {
        id: 4,
        name: "Cargo Utility Pants",
        category: "pants",
        price: 79.99,
        originalPrice: 99.99,
        image: "https://images.unsplash.com/photo-1473966968600-fa801b869a1a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 203,
        colors: ["black", "green", "gray"],
        sizes: ["S", "M", "L", "XL", "XXL"],
        badge: null,
        inStock: true
    },
    {
        id: 5,
        name: "Signature Snapback",
        category: "accessories",
        price: 35.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1588850561407-ed78c282e89b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 156,
        colors: ["black", "white", "red"],
        sizes: ["One Size"],
        badge: "bestseller",
        inStock: true
    },
    {
        id: 6,
        name: "Oversized Bomber",
        category: "jackets",
        price: 129.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1551028719-00167b16eac5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 78,
        colors: ["black", "green", "blue"],
        sizes: ["M", "L", "XL"],
        badge: null,
        inStock: true
    }
];

// DOM Elements
const productsGrid = document.getElementById('productsGrid');
const filterToggle = document.getElementById('filterToggle');
const filterPanel = document.getElementById('filterPanel');
const sortSelect = document.getElementById('sortSelect');
const loadMoreBtn = document.getElementById('loadMoreBtn');
const priceRange = document.getElementById('priceRange');
const priceValue = document.getElementById('priceValue');

// State
let currentProducts = [...clothingProducts];
let displayedProducts = 6;
let filters = {
    categories: ['hoodies'],
    sizes: ['M'],
    colors: [],
    priceMax: 250,
    sortBy: 'featured'
};

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    renderProducts();
    setupEventListeners();
    updateFiltersFromState();
});

// Event Listeners
function setupEventListeners() {
    // Filter toggle
    filterToggle.addEventListener('click', () => {
        filterPanel.classList.toggle('active');
        filterToggle.classList.toggle('active');
    });

    // Sort select
    sortSelect.addEventListener('change', (e) => {
        filters.sortBy = e.target.value;
        applyFilters();
    });

    // Price range
    if (priceRange) {
        priceRange.addEventListener('input', (e) => {
            const value = e.target.value;
            priceValue.textContent = `$${value}`;
            filters.priceMax = parseInt(value);
        });

        priceRange.addEventListener('change', () => {
            applyFilters();
        });
    }

    // Category checkboxes
    document.querySelectorAll('input[name="category"]').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const category = e.target.value;
            if (e.target.checked) {
                if (!filters.categories.includes(category)) {
                    filters.categories.push(category);
                }
            } else {
                filters.categories = filters.categories.filter(c => c !== category);
            }
            applyFilters();
        });
    });

    // Size buttons
    document.querySelectorAll('.size-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const size = e.target.textContent;
            
            // Toggle active state
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                if (!filters.sizes.includes(size)) {
                    filters.sizes.push(size);
                }
            } else {
                filters.sizes = filters.sizes.filter(s => s !== size);
            }
            applyFilters();
        });
    });

    // Color buttons
    document.querySelectorAll('.color-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const color = e.target.dataset.color;
            
            // Toggle active state
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                if (!filters.colors.includes(color)) {
                    filters.colors.push(color);
                }
            } else {
                filters.colors = filters.colors.filter(c => c !== color);
            }
            applyFilters();
        });
    });

    // Clear filters
    document.querySelector('.clear-filters').addEventListener('click', () => {
        clearAllFilters();
    });

    // Apply filters
    document.querySelector('.apply-filters').addEventListener('click', () => {
        applyFilters();
        filterPanel.classList.remove('active');
        filterToggle.classList.remove('active');
    });

    // Load more
    loadMoreBtn.addEventListener('click', () => {
        displayedProducts += 6;
        renderProducts();
    });
}

// Update filters UI from state
function updateFiltersFromState() {
    // Update category checkboxes
    document.querySelectorAll('input[name="category"]').forEach(checkbox => {
        checkbox.checked = filters.categories.includes(checkbox.value);
    });

    // Update size buttons
    document.querySelectorAll('.size-btn').forEach(btn => {
        if (filters.sizes.includes(btn.textContent)) {
            btn.classList.add('active');
        }
    });

    // Update price range
    if (priceRange) {
        priceRange.value = filters.priceMax;
        priceValue.textContent = `$${filters.priceMax}`;
    }
}

// Apply filters and sorting
function applyFilters() {
    let filtered = [...clothingProducts];

    // Filter by categories
    if (filters.categories.length > 0) {
        filtered = filtered.filter(product => 
            filters.categories.includes(product.category)
        );
    }

    // Filter by sizes
    if (filters.sizes.length > 0) {
        filtered = filtered.filter(product => 
            product.sizes.some(size => filters.sizes.includes(size))
        );
    }

    // Filter by colors
    if (filters.colors.length > 0) {
        filtered = filtered.filter(product => 
            product.colors.some(color => filters.colors.includes(color))
        );
    }

    // Filter by price
    filtered = filtered.filter(product => product.price <= filters.priceMax);

    // Sort products
    switch (filters.sortBy) {
        case 'newest':
            filtered.sort((a, b) => b.id - a.id);
            break;
        case 'price-low':
            filtered.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            filtered.sort((a, b) => b.price - a.price);
            break;
        case 'rating':
            filtered.sort((a, b) => b.rating - a.rating);
            break;
        default:
            // Featured - keep original order
            break;
    }

    currentProducts = filtered;
    displayedProducts = 6;
    renderProducts();
    updateResultsCount();
}

// Clear all filters
function clearAllFilters() {
    filters = {
        categories: [],
        sizes: [],
        colors: [],
        priceMax: 500,
        sortBy: 'featured'
    };

    // Update UI
    document.querySelectorAll('input[name="category"]').forEach(checkbox => {
        checkbox.checked = false;
    });

    document.querySelectorAll('.size-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    document.querySelectorAll('.color-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    if (priceRange) {
        priceRange.value = 500;
        priceValue.textContent = '$500';
    }

    sortSelect.value = 'featured';

    applyFilters();
}

// Update results count
function updateResultsCount() {
    const resultsCount = document.querySelector('.results-count span');
    const showing = Math.min(displayedProducts, currentProducts.length);
    resultsCount.textContent = `Showing ${showing} of ${currentProducts.length} products`;
}

// Render products
function renderProducts() {
    const productsToShow = currentProducts.slice(0, displayedProducts);
    
    productsGrid.innerHTML = productsToShow.map(product => `
        <div class="product-card" data-product-id="${product.id}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" loading="lazy">
                <div class="product-overlay">
                    <button class="quick-view-btn" onclick="quickView(${product.id})">
                        <i class="fas fa-eye"></i>
                        Quick View
                    </button>
                    <button class="add-to-cart-btn" onclick="addToCart(${product.id})">
                        <i class="fas fa-shopping-cart"></i>
                        Add to Cart
                    </button>
                    <button class="wishlist-btn-product" onclick="toggleWishlist(${product.id})">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>
                ${product.badge ? `<span class="product-badge ${product.badge}">${product.badge}</span>` : ''}
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-category">${product.category.charAt(0).toUpperCase() + product.category.slice(1)}</p>
                <div class="product-price">
                    <span class="current-price">$${product.price}</span>
                    ${product.originalPrice ? `<span class="original-price">$${product.originalPrice}</span>` : ''}
                </div>
                <div class="product-rating">
                    <div class="stars">
                        ${generateStars(product.rating)}
                    </div>
                    <span class="rating-count">(${product.reviews})</span>
                </div>
            </div>
        </div>
    `).join('');

    // Show/hide load more button
    if (displayedProducts >= currentProducts.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'inline-flex';
    }

    updateResultsCount();
}

// Generate star rating HTML
function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    return stars;
}

// Product actions
function quickView(productId) {
    const product = clothingProducts.find(p => p.id === productId);
    // In a real app, this would open a modal or navigate to product detail page
    alert(`Quick view for: ${product.name}\nPrice: $${product.price}\nRating: ${product.rating}/5 stars`);
}

function addToCart(productId) {
    const product = clothingProducts.find(p => p.id === productId);
    // In a real app, this would add to cart state/localStorage
    alert(`Added "${product.name}" to cart!`);
    
    // Update cart badge
    const cartBadge = document.querySelector('.cart-btn .badge');
    if (cartBadge) {
        const currentCount = parseInt(cartBadge.textContent);
        cartBadge.textContent = currentCount + 1;
    }
}

function toggleWishlist(productId) {
    const product = clothingProducts.find(p => p.id === productId);
    // In a real app, this would toggle wishlist state
    alert(`Toggled wishlist for: ${product.name}`);
    
    // Update wishlist badge
    const wishlistBadge = document.querySelector('.wishlist-btn .badge');
    if (wishlistBadge) {
        const currentCount = parseInt(wishlistBadge.textContent);
        wishlistBadge.textContent = Math.max(0, currentCount + (Math.random() > 0.5 ? 1 : -1));
    }
}
