/* Product Pages Styles */

/* Active Navigation Link */
.nav-link.active {
    color: var(--neutral-900);
    font-weight: 600;
}

.nav-link.active::after {
    width: 100%;
}

/* Page Header */
.page-header {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, var(--neutral-100), var(--neutral-50));
    text-align: center;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--neutral-500);
}

.breadcrumb a {
    color: var(--neutral-600);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--neutral-900);
}

.page-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: bold;
    color: var(--neutral-900);
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: var(--neutral-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Filters Section */
.filters-section {
    padding: 2rem 0;
    background: white;
    border-bottom: 1px solid var(--neutral-200);
    position: sticky;
    top: 4rem;
    z-index: 100;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.results-count {
    color: var(--neutral-600);
    font-weight: 500;
}

.filters-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.filter-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--neutral-100);
    border: none;
    border-radius: 0.5rem;
    color: var(--neutral-700);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.filter-toggle:hover {
    background: var(--neutral-200);
}

.filter-toggle.active {
    background: var(--neutral-900);
    color: white;
}

.sort-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    background: white;
    color: var(--neutral-700);
    font-weight: 500;
    cursor: pointer;
    outline: none;
    transition: var(--transition-fast);
}

.sort-select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Filter Panel */
.filter-panel {
    display: none;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    padding: 2rem;
    background: var(--neutral-50);
    border-radius: 1rem;
    margin-top: 1rem;
}

.filter-panel.active {
    display: grid;
}

.filter-group {
    background: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filter-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1rem;
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: var(--neutral-700);
    transition: var(--transition-fast);
}

.filter-option:hover {
    color: var(--neutral-900);
}

.filter-option input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: 0.25rem;
    position: relative;
    transition: var(--transition-fast);
}

.filter-option input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.filter-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

/* Size Options */
.size-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.size-btn {
    width: 3rem;
    height: 3rem;
    border: 2px solid var(--neutral-300);
    background: white;
    border-radius: 0.5rem;
    color: var(--neutral-700);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.size-btn:hover {
    border-color: var(--neutral-400);
}

.size-btn.active {
    border-color: var(--primary-500);
    background: var(--primary-500);
    color: white;
}

/* Price Range */
.price-range {
    margin-top: 0.5rem;
}

.price-slider {
    width: 100%;
    height: 0.5rem;
    border-radius: 0.25rem;
    background: var(--neutral-200);
    outline: none;
    -webkit-appearance: none;
    margin-bottom: 1rem;
}

.price-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    background: var(--primary-500);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.price-slider::-moz-range-thumb {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    background: var(--primary-500);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.price-values {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--neutral-600);
}

/* Color Options */
.color-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.color-btn {
    width: 2.5rem;
    height: 2.5rem;
    border: 3px solid transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
}

.color-btn:hover {
    transform: scale(1.1);
}

.color-btn.active {
    border-color: var(--neutral-900);
}

.color-btn::after {
    content: '';
    position: absolute;
    inset: -6px;
    border: 2px solid transparent;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.color-btn.active::after {
    border-color: var(--primary-500);
}

/* Filter Actions */
.filter-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.filter-actions .btn {
    flex: 1;
}

/* Products Section */
.products-section {
    padding: 3rem 0;
    background: var(--neutral-50);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Enhanced Product Card */
.product-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.quick-view-btn,
.add-to-cart-btn,
.wishlist-btn-product {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    background: white;
    color: var(--neutral-900);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.wishlist-btn-product {
    padding: 0.75rem;
    width: auto;
    aspect-ratio: 1;
}

.quick-view-btn:hover,
.add-to-cart-btn:hover,
.wishlist-btn-product:hover {
    background: var(--neutral-100);
    transform: scale(1.05);
}

.add-to-cart-btn {
    background: var(--primary-500);
    color: white;
}

.add-to-cart-btn:hover {
    background: var(--primary-600);
}

/* Load More */
.load-more-section {
    text-align: center;
}

.load-more-btn {
    min-width: 200px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filters-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .filters-controls {
        justify-content: space-between;
    }
    
    .filter-panel {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }
    
    .filter-actions {
        flex-direction: column;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .size-options {
        justify-content: center;
    }
    
    .color-options {
        justify-content: center;
    }
}

/* Coming Soon Page */
.coming-soon-section {
    padding: 3rem 0;
    background: var(--neutral-50);
    min-height: 70vh;
}

.coming-soon-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.coming-soon-icon {
    font-size: 4rem;
    color: var(--primary-500);
    margin-bottom: 2rem;
}

.coming-soon-content h2 {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--neutral-900);
    margin-bottom: 1.5rem;
}

.coming-soon-content > p {
    font-size: 1.125rem;
    color: var(--neutral-600);
    line-height: 1.7;
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.feature-item {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: var(--transition-normal);
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.feature-item i {
    font-size: 2rem;
    color: var(--primary-500);
    margin-bottom: 1rem;
}

.feature-item h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
}

.feature-item p {
    color: var(--neutral-600);
    font-size: 0.875rem;
}

.notify-section {
    background: white;
    padding: 3rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 4rem;
}

.notify-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
}

.notify-section p {
    color: var(--neutral-600);
    margin-bottom: 2rem;
}

.notify-form {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

.notify-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    outline: none;
    transition: var(--transition-fast);
}

.notify-input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.preview-section {
    margin-bottom: 3rem;
}

.preview-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 2rem;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.preview-item {
    aspect-ratio: 1;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition-normal);
}

.preview-item:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.back-shopping {
    margin-top: 3rem;
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .notify-form {
        flex-direction: column;
    }

    .preview-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .notify-section {
        padding: 2rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 6rem 0 3rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-panel {
        padding: 0.5rem;
    }

    .filter-group {
        padding: 1rem;
    }

    .coming-soon-content h2 {
        font-size: 2rem;
    }

    .feature-item {
        padding: 1.5rem;
    }

    .notify-section {
        padding: 1.5rem;
    }

    .preview-grid {
        grid-template-columns: 1fr;
    }
}
