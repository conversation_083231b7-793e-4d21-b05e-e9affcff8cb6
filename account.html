<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Account - Magna5RRR</title>
    <meta name="description" content="Manage your Magna5RRR account, orders, and preferences.">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <!-- Logo -->
                <div class="nav-logo">
                    <a href="index.html" class="logo-link">
                        <div class="logo-icon">M5</div>
                        <span class="logo-text">Magna5RRR</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="clothing.html" class="nav-link">Clothing</a>
                    </li>
                    <li class="nav-item">
                        <a href="perfumes.html" class="nav-link">Perfumes</a>
                    </li>
                    <li class="nav-item">
                        <a href="jewelry.html" class="nav-link">Jewelry</a>
                    </li>
                    <li class="nav-item">
                        <a href="essentials.html" class="nav-link">Essentials</a>
                    </li>
                    <li class="nav-item">
                        <a href="collections.html" class="nav-link">Collections</a>
                    </li>
                </ul>

                <!-- Right side icons -->
                <div class="nav-actions">
                    <button class="nav-icon search-btn" id="searchBtn">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="wishlist.html" class="nav-icon wishlist-btn">
                        <i class="fas fa-heart"></i>
                        <span class="badge">2</span>
                    </a>
                    <a href="cart.html" class="nav-icon cart-btn">
                        <i class="fas fa-shopping-bag"></i>
                        <span class="badge">3</span>
                    </a>
                    <a href="account.html" class="nav-icon account-btn active">
                        <i class="fas fa-user"></i>
                    </a>
                    <button class="nav-toggle" id="navToggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="search-bar" id="searchBar">
                <div class="search-container">
                    <input type="text" placeholder="Search for products..." class="search-input">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div class="mobile-menu" id="mobileMenu">
                <a href="clothing.html" class="mobile-link">Clothing</a>
                <a href="perfumes.html" class="mobile-link">Perfumes</a>
                <a href="jewelry.html" class="mobile-link">Jewelry</a>
                <a href="essentials.html" class="mobile-link">Essentials</a>
                <a href="collections.html" class="mobile-link">Collections</a>
            </div>
        </nav>
    </header>

    <!-- Account Section -->
    <section class="account-section">
        <div class="container">
            <!-- Not Logged In View -->
            <div class="auth-container" id="authContainer">
                <div class="auth-tabs">
                    <button class="auth-tab active" data-tab="signin">Sign In</button>
                    <button class="auth-tab" data-tab="signup">Sign Up</button>
                </div>

                <!-- Sign In Form -->
                <div class="auth-form-container" id="signinForm">
                    <h2 class="auth-title">Welcome Back</h2>
                    <p class="auth-subtitle">Sign in to your Magna5RRR account</p>
                    
                    <form class="auth-form" id="signinFormElement">
                        <div class="form-group">
                            <label for="signinEmail">Email Address</label>
                            <input type="email" id="signinEmail" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="signinPassword">Password</label>
                            <div class="password-input">
                                <input type="password" id="signinPassword" name="password" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('signinPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" name="remember">
                                <span class="checkmark"></span>
                                Remember me
                            </label>
                            <a href="#" class="forgot-password" onclick="showForgotPassword()">Forgot Password?</a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary auth-btn">
                            <i class="fas fa-sign-in-alt"></i>
                            Sign In
                        </button>
                    </form>
                    
                    <div class="social-auth">
                        <p>Or sign in with</p>
                        <div class="social-buttons">
                            <button class="social-btn google-btn">
                                <i class="fab fa-google"></i>
                                Google
                            </button>
                            <button class="social-btn facebook-btn">
                                <i class="fab fa-facebook-f"></i>
                                Facebook
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sign Up Form -->
                <div class="auth-form-container hidden" id="signupForm">
                    <h2 class="auth-title">Create Account</h2>
                    <p class="auth-subtitle">Join the Magna5RRR community</p>
                    
                    <form class="auth-form" id="signupFormElement">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">First Name</label>
                                <input type="text" id="firstName" name="firstName" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name</label>
                                <input type="text" id="lastName" name="lastName" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="signupEmail">Email Address</label>
                            <input type="email" id="signupEmail" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="signupPassword">Password</label>
                            <div class="password-input">
                                <input type="password" id="signupPassword" name="password" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('signupPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength" id="passwordStrength"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password</label>
                            <div class="password-input">
                                <input type="password" id="confirmPassword" name="confirmPassword" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="terms" required>
                                <span class="checkmark"></span>
                                I agree to the <a href="terms.html">Terms of Service</a> and <a href="privacy.html">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="newsletter">
                                <span class="checkmark"></span>
                                Subscribe to newsletter for exclusive offers
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary auth-btn">
                            <i class="fas fa-user-plus"></i>
                            Create Account
                        </button>
                    </form>
                </div>

                <!-- Forgot Password Form -->
                <div class="auth-form-container hidden" id="forgotPasswordForm">
                    <h2 class="auth-title">Reset Password</h2>
                    <p class="auth-subtitle">Enter your email to receive a reset link</p>
                    
                    <form class="auth-form" id="forgotPasswordFormElement">
                        <div class="form-group">
                            <label for="resetEmail">Email Address</label>
                            <input type="email" id="resetEmail" name="email" required>
                        </div>
                        
                        <button type="submit" class="btn btn-primary auth-btn">
                            <i class="fas fa-paper-plane"></i>
                            Send Reset Link
                        </button>
                        
                        <button type="button" class="btn btn-outline" onclick="showSignIn()">
                            <i class="fas fa-arrow-left"></i>
                            Back to Sign In
                        </button>
                    </form>
                </div>
            </div>

            <!-- Logged In Dashboard -->
            <div class="dashboard-container hidden" id="dashboardContainer">
                <div class="dashboard-sidebar">
                    <div class="user-profile">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-info">
                            <h3 id="userName">John Doe</h3>
                            <p id="userEmail"><EMAIL></p>
                        </div>
                    </div>
                    
                    <nav class="dashboard-nav">
                        <a href="#" class="nav-item active" data-section="overview">
                            <i class="fas fa-tachometer-alt"></i>
                            Overview
                        </a>
                        <a href="#" class="nav-item" data-section="orders">
                            <i class="fas fa-shopping-bag"></i>
                            Orders
                        </a>
                        <a href="#" class="nav-item" data-section="wishlist">
                            <i class="fas fa-heart"></i>
                            Wishlist
                        </a>
                        <a href="#" class="nav-item" data-section="addresses">
                            <i class="fas fa-map-marker-alt"></i>
                            Addresses
                        </a>
                        <a href="#" class="nav-item" data-section="profile">
                            <i class="fas fa-user-edit"></i>
                            Profile
                        </a>
                        <a href="#" class="nav-item" data-section="security">
                            <i class="fas fa-shield-alt"></i>
                            Security
                        </a>
                        <a href="#" class="nav-item" onclick="signOut()">
                            <i class="fas fa-sign-out-alt"></i>
                            Sign Out
                        </a>
                    </nav>
                </div>
                
                <div class="dashboard-content">
                    <!-- Overview Section -->
                    <div class="dashboard-section active" id="overviewSection">
                        <h2>Account Overview</h2>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-shopping-bag"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>12</h3>
                                    <p>Total Orders</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-heart"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>8</h3>
                                    <p>Wishlist Items</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>$1,247</h3>
                                    <p>Total Spent</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="recent-orders">
                            <h3>Recent Orders</h3>
                            <div class="order-list" id="recentOrdersList">
                                <!-- Orders will be loaded here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Other sections will be loaded dynamically -->
                    <div class="dashboard-section" id="ordersSection">
                        <h2>Order History</h2>
                        <div class="orders-content" id="ordersContent">
                            <!-- Orders content will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="dashboard-section" id="profileSection">
                        <h2>Profile Settings</h2>
                        <div class="profile-content" id="profileContent">
                            <!-- Profile content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <div class="logo-icon">M5</div>
                        <span class="logo-text">Magna5RRR</span>
                    </div>
                    <p class="footer-description">
                        Where streetwear meets luxury. Discover collections that define your unique style.
                    </p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3 class="footer-title">Shop</h3>
                    <ul class="footer-links">
                        <li><a href="clothing.html">Clothing</a></li>
                        <li><a href="perfumes.html">Perfumes</a></li>
                        <li><a href="jewelry.html">Jewelry</a></li>
                        <li><a href="essentials.html">Essentials</a></li>
                        <li><a href="collections.html">Collections</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3 class="footer-title">Support</h3>
                    <ul class="footer-links">
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="shipping.html">Shipping Info</a></li>
                        <li><a href="returns.html">Returns</a></li>
                        <li><a href="size-guide.html">Size Guide</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3 class="footer-title">Company</h3>
                    <ul class="footer-links">
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="careers.html">Careers</a></li>
                        <li><a href="press.html">Press</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 Magna5RRR. All rights reserved.</p>
                <div class="payment-methods">
                    <i class="fab fa-cc-visa"></i>
                    <i class="fab fa-cc-mastercard"></i>
                    <i class="fab fa-cc-amex"></i>
                    <i class="fab fa-cc-paypal"></i>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
    <script src="auth.js"></script>
</body>
</html>
