/* Support Pages Styles */

/* Contact Section */
.contact-section {
    padding: 3rem 0;
    background: var(--neutral-50);
}

.contact-layout {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 4rem;
    align-items: start;
}

/* Contact Info */
.contact-info {
    background: white;
    padding: 2.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
}

.contact-info h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1rem;
}

.contact-info > p {
    color: var(--neutral-600);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-methods {
    margin-bottom: 2.5rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--neutral-100);
}

.contact-method:last-child {
    border-bottom: none;
}

.method-icon {
    width: 3rem;
    height: 3rem;
    background: var(--primary-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    font-size: 1.25rem;
    flex-shrink: 0;
}

.method-details h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.25rem;
}

.method-details p {
    color: var(--neutral-700);
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.method-details span {
    color: var(--neutral-500);
    font-size: 0.875rem;
}

/* Social Section */
.social-section {
    padding-top: 2rem;
    border-top: 1px solid var(--neutral-200);
}

.social-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--neutral-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--neutral-600);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-link:hover {
    background: var(--primary-500);
    color: white;
    transform: translateY(-2px);
}

/* Contact Form */
.contact-form-section {
    background: white;
    padding: 2.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.form-header {
    margin-bottom: 2rem;
}

.form-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
}

.form-header p {
    color: var(--neutral-600);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: var(--neutral-700);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: var(--transition-fast);
    outline: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: var(--neutral-600);
    font-size: 0.875rem;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-label .checkmark {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: 0.25rem;
    position: relative;
    transition: var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

.submit-btn {
    margin-top: 1rem;
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
}

/* FAQ Quick Access */
.faq-quick {
    padding: 3rem 0;
    background: white;
}

.faq-quick h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
}

.faq-quick p {
    text-align: center;
    color: var(--neutral-600);
    margin-bottom: 3rem;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.faq-card {
    background: var(--neutral-50);
    padding: 2rem;
    border-radius: 1rem;
    text-decoration: none;
    color: inherit;
    transition: var(--transition-normal);
    text-align: center;
    border: 1px solid var(--neutral-200);
}

.faq-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-300);
}

.faq-card i {
    font-size: 2.5rem;
    color: var(--primary-500);
    margin-bottom: 1rem;
}

.faq-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: 0.5rem;
}

.faq-card p {
    color: var(--neutral-600);
    font-size: 0.875rem;
    margin: 0;
}

/* AI Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 100px;
    right: 2rem;
    width: 350px;
    max-height: 500px;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.chat-widget.active {
    display: flex;
}

.chat-header {
    background: var(--neutral-900);
    color: white;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.chat-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-info h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.chat-status {
    font-size: 0.75rem;
    color: var(--primary-300);
}

.chat-toggle {
    margin-left: auto;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.25rem;
}

.chat-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-height: 400px;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    max-height: 300px;
}

.message {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.message-avatar {
    width: 2rem;
    height: 2rem;
    background: var(--primary-500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--neutral-600);
}

.message-content {
    flex: 1;
}

.message-content p {
    background: var(--neutral-100);
    padding: 0.75rem;
    border-radius: 0.75rem;
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.4;
}

.user-message .message-content p {
    background: var(--primary-500);
    color: white;
}

.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.quick-action {
    padding: 0.5rem 0.75rem;
    background: var(--primary-100);
    color: var(--primary-700);
    border: none;
    border-radius: 1rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.quick-action:hover {
    background: var(--primary-200);
}

.chat-input-section {
    padding: 1rem;
    border-top: 1px solid var(--neutral-200);
}

.chat-input-container {
    display: flex;
    gap: 0.5rem;
}

.chat-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--neutral-300);
    border-radius: 1.5rem;
    outline: none;
    font-size: 0.875rem;
}

.chat-input:focus {
    border-color: var(--primary-500);
}

.chat-send {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-500);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition-fast);
}

.chat-send:hover {
    background: var(--primary-600);
}

/* Chat Trigger Button */
.chat-trigger {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    background: var(--primary-500);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(14, 165, 233, 0.3);
    transition: var(--transition-normal);
    z-index: 999;
    font-size: 1.25rem;
}

.chat-trigger:hover {
    background: var(--primary-600);
    transform: scale(1.1);
}

.chat-notification {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    width: 1.25rem;
    height: 1.25rem;
    background: var(--accent-500);
    color: white;
    border-radius: 50%;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .contact-info,
    .contact-form-section {
        padding: 1.5rem;
    }
    
    .faq-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .chat-widget {
        width: calc(100vw - 2rem);
        right: 1rem;
        left: 1rem;
    }
    
    .chat-trigger {
        bottom: 1rem;
        right: 1rem;
    }
}

@media (max-width: 480px) {
    .contact-info,
    .contact-form-section {
        padding: 1rem;
    }
    
    .faq-card {
        padding: 1.5rem;
    }
    
    .contact-method {
        padding: 1rem 0;
    }
    
    .method-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
}
