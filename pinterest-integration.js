// Pinterest Integration for Streetwear Slideshow
class PinterestIntegration {
    constructor() {
        this.pinterestBoardId = 'magna5rrr/streetwear-inspiration'; // Your Pinterest board
        this.apiKey = 'your-pinterest-api-key'; // Replace with actual API key
        this.accessToken = 'your-access-token'; // Replace with actual access token
        this.cachedPins = [];
        this.isLoaded = false;
        
        this.init();
    }

    init() {
        this.loadPinterestAPI();
        this.setupPinterestIntegration();
    }

    loadPinterestAPI() {
        // Load Pinterest SDK
        if (!window.PDK) {
            const script = document.createElement('script');
            script.src = 'https://assets.pinterest.com/sdk/sdk.js';
            script.async = true;
            script.onload = () => {
                this.initializePinterestSDK();
            };
            document.head.appendChild(script);
        } else {
            this.initializePinterestSDK();
        }
    }

    initializePinterestSDK() {
        if (window.PDK) {
            window.PDK.init({
                appId: this.apiKey,
                cookie: true
            });
            this.isLoaded = true;
            this.fetchStreetwarePins();
        }
    }

    setupPinterestIntegration() {
        // Add Pinterest-inspired slides to the slideshow
        this.addPinterestSlides();
        this.setupPinterestButtons();
    }

    addPinterestSlides() {
        // Enhanced slideshow data with Pinterest-style content
        const pinterestSlides = [
            {
                id: 'pinterest_1',
                type: 'streetwear',
                title: 'LOUIS VUITTON SS25',
                subtitle: 'Pharrell Williams Collection',
                description: 'The intersection of luxury and street culture. Monogram meets urban rebellion.',
                image: 'https://images.unsplash.com/photo-1556821840-3a9c6dcb0e78?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                overlay: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'SHOP LV',
                link: 'clothing.html?brand=louis-vuitton',
                mood: 'energetic',
                pinterest: {
                    boardUrl: 'https://pinterest.com/magna5rrr/louis-vuitton-streetwear',
                    pinCount: 247,
                    category: 'luxury-streetwear'
                }
            },
            {
                id: 'pinterest_2',
                type: 'art',
                title: 'CORTEIZ UNDERGROUND',
                subtitle: 'London Street Culture',
                description: 'Raw energy from the UK underground scene. Alcatraz meets high fashion.',
                image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                overlay: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'EXPLORE CORTEIZ',
                link: 'clothing.html?brand=corteiz',
                mood: 'mysterious',
                pinterest: {
                    boardUrl: 'https://pinterest.com/magna5rrr/corteiz-streetwear',
                    pinCount: 189,
                    category: 'underground-fashion'
                }
            },
            {
                id: 'pinterest_3',
                type: 'luxury',
                title: 'PRADA TECHNICAL',
                subtitle: 'Re-Nylon Revolution',
                description: 'Sustainable luxury meets street functionality. The future of fashion.',
                image: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                overlay: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'SHOP PRADA',
                link: 'clothing.html?brand=prada',
                mood: 'calm',
                pinterest: {
                    boardUrl: 'https://pinterest.com/magna5rrr/prada-technical',
                    pinCount: 156,
                    category: 'luxury-technical'
                }
            },
            {
                id: 'pinterest_4',
                type: 'emerging',
                title: 'HOUSE OF ERRORS',
                subtitle: 'Art Meets Fashion',
                description: 'Gallery-worthy pieces that challenge conventional streetwear boundaries.',
                image: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                overlay: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'VIEW COLLECTION',
                link: 'clothing.html?brand=house-of-errors',
                mood: 'energetic',
                pinterest: {
                    boardUrl: 'https://pinterest.com/magna5rrr/house-of-errors',
                    pinCount: 98,
                    category: 'art-fashion'
                }
            },
            {
                id: 'pinterest_5',
                type: 'collective',
                title: '3PARADIS COLLECTIVE',
                subtitle: 'French Underground',
                description: 'Parisian street culture meets contemporary design. Paradise found.',
                image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                overlay: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
                cta: 'DISCOVER 3PARADIS',
                link: 'clothing.html?brand=3paradis',
                mood: 'mysterious',
                pinterest: {
                    boardUrl: 'https://pinterest.com/magna5rrr/3paradis-collective',
                    pinCount: 134,
                    category: 'french-streetwear'
                }
            }
        ];

        // Update slideshow with Pinterest data
        if (window.streetwareSlideshow) {
            window.streetwareSlideshow.slides = pinterestSlides;
            window.streetwareSlideshow.updateSlideshow();
        }
    }

    setupPinterestButtons() {
        // Add Pinterest save buttons to product cards
        document.addEventListener('DOMContentLoaded', () => {
            this.addPinterestSaveButtons();
            this.addPinterestBoardLinks();
        });
    }

    addPinterestSaveButtons() {
        const productCards = document.querySelectorAll('.product-card');
        
        productCards.forEach(card => {
            const productImage = card.querySelector('img');
            const productName = card.querySelector('.product-name')?.textContent;
            const productPrice = card.querySelector('.current-price')?.textContent;
            
            if (productImage) {
                const pinterestBtn = document.createElement('button');
                pinterestBtn.className = 'pinterest-save-btn';
                pinterestBtn.innerHTML = '<i class="fab fa-pinterest"></i>';
                pinterestBtn.title = 'Save to Pinterest';
                
                pinterestBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.saveToPinterest(productImage.src, productName, productPrice);
                });
                
                const overlay = card.querySelector('.product-overlay');
                if (overlay) {
                    overlay.appendChild(pinterestBtn);
                }
            }
        });
    }

    addPinterestBoardLinks() {
        // Add Pinterest board links to slideshow
        const slideshowContainer = document.querySelector('.streetwear-slideshow');
        if (slideshowContainer) {
            const pinterestLink = document.createElement('a');
            pinterestLink.href = 'https://pinterest.com/magna5rrr/streetwear-inspiration';
            pinterestLink.target = '_blank';
            pinterestLink.className = 'pinterest-board-link';
            pinterestLink.innerHTML = `
                <i class="fab fa-pinterest"></i>
                <span>VIEW FULL BOARD</span>
            `;
            
            slideshowContainer.appendChild(pinterestLink);
        }
    }

    saveToPinterest(imageUrl, description, price) {
        if (!window.PDK || !this.isLoaded) {
            // Fallback to Pinterest URL scheme
            this.saveToPinterestFallback(imageUrl, description, price);
            return;
        }

        const pinData = {
            media: imageUrl,
            description: `${description} - ${price} | Magna5RRR Streetwear`,
            url: window.location.href
        };

        window.PDK.pin(pinData, (response) => {
            if (response.success) {
                this.showPinterestSuccess();
            } else {
                this.saveToPinterestFallback(imageUrl, description, price);
            }
        });
    }

    saveToPinterestFallback(imageUrl, description, price) {
        const pinterestUrl = `https://pinterest.com/pin/create/button/?` +
            `url=${encodeURIComponent(window.location.href)}&` +
            `media=${encodeURIComponent(imageUrl)}&` +
            `description=${encodeURIComponent(`${description} - ${price} | Magna5RRR Streetwear`)}`;
        
        window.open(pinterestUrl, '_blank', 'width=750,height=320');
    }

    fetchStreetwarePins() {
        // Fetch pins from your Pinterest board (requires API access)
        // This is a placeholder for actual Pinterest API integration
        console.log('Fetching streetwear inspiration from Pinterest...');
        
        // Simulate API response
        setTimeout(() => {
            this.cachedPins = [
                {
                    id: '1',
                    image: 'https://images.unsplash.com/photo-1556821840-3a9c6dcb0e78',
                    description: 'Louis Vuitton Streetwear Inspiration',
                    url: 'https://pinterest.com/pin/123456789'
                },
                {
                    id: '2',
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96',
                    description: 'Corteiz Underground Fashion',
                    url: 'https://pinterest.com/pin/987654321'
                }
            ];
            
            this.displayPinterestInspiration();
        }, 1000);
    }

    displayPinterestInspiration() {
        // Create Pinterest inspiration section
        const inspirationSection = document.createElement('section');
        inspirationSection.className = 'pinterest-inspiration';
        inspirationSection.innerHTML = `
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">STREET INSPIRATION</h2>
                    <p class="section-subtitle">Curated from our Pinterest boards</p>
                </div>
                <div class="pinterest-grid" id="pinterestGrid">
                    ${this.cachedPins.map(pin => `
                        <div class="pinterest-pin">
                            <img src="${pin.image}" alt="${pin.description}">
                            <div class="pin-overlay">
                                <a href="${pin.url}" target="_blank" class="pin-link">
                                    <i class="fab fa-pinterest"></i>
                                    View on Pinterest
                                </a>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="pinterest-cta">
                    <a href="https://pinterest.com/magna5rrr" target="_blank" class="btn-street">
                        FOLLOW ON PINTEREST
                    </a>
                </div>
            </div>
        `;
        
        // Insert before footer
        const footer = document.querySelector('.footer');
        if (footer) {
            footer.parentNode.insertBefore(inspirationSection, footer);
        }
    }

    showPinterestSuccess() {
        const message = document.createElement('div');
        message.className = 'pinterest-success-message';
        message.innerHTML = `
            <i class="fab fa-pinterest"></i>
            <span>Saved to Pinterest!</span>
        `;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 3000);
    }

    // Public API methods
    updateSlideshow() {
        this.addPinterestSlides();
    }

    getPinterestBoards() {
        return [
            'streetwear-inspiration',
            'louis-vuitton-streetwear',
            'corteiz-streetwear',
            'prada-technical',
            'house-of-errors',
            '3paradis-collective'
        ];
    }
}

// Initialize Pinterest Integration
document.addEventListener('DOMContentLoaded', () => {
    window.pinterestIntegration = new PinterestIntegration();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PinterestIntegration;
}
