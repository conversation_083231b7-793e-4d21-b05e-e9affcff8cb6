// DOM Elements
const navToggle = document.getElementById('navToggle');
const mobileMenu = document.getElementById('mobileMenu');
const searchBtn = document.getElementById('searchBtn');
const searchBar = document.getElementById('searchBar');
const header = document.querySelector('.header');

// Mobile Navigation Toggle
navToggle.addEventListener('click', () => {
    mobileMenu.classList.toggle('active');
    navToggle.classList.toggle('active');
    
    // Animate hamburger menu
    const spans = navToggle.querySelectorAll('span');
    if (navToggle.classList.contains('active')) {
        spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
        spans[1].style.opacity = '0';
        spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
    } else {
        spans[0].style.transform = 'none';
        spans[1].style.opacity = '1';
        spans[2].style.transform = 'none';
    }
});

// Search Toggle
searchBtn.addEventListener('click', () => {
    searchBar.classList.toggle('active');
    if (searchBar.classList.contains('active')) {
        searchBar.querySelector('.search-input').focus();
    }
});

// Close mobile menu when clicking on links
document.querySelectorAll('.mobile-link').forEach(link => {
    link.addEventListener('click', () => {
        mobileMenu.classList.remove('active');
        navToggle.classList.remove('active');
        
        const spans = navToggle.querySelectorAll('span');
        spans[0].style.transform = 'none';
        spans[1].style.opacity = '1';
        spans[2].style.transform = 'none';
    });
});

// Header scroll effect
let lastScrollY = window.scrollY;

window.addEventListener('scroll', () => {
    const currentScrollY = window.scrollY;
    
    if (currentScrollY > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
    } else {
        header.style.background = 'rgba(255, 255, 255, 0.9)';
        header.style.boxShadow = 'none';
    }
    
    // Hide/show header on scroll
    if (currentScrollY > lastScrollY && currentScrollY > 200) {
        header.style.transform = 'translateY(-100%)';
    } else {
        header.style.transform = 'translateY(0)';
    }
    
    lastScrollY = currentScrollY;
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            const headerHeight = header.offsetHeight;
            const targetPosition = target.offsetTop - headerHeight;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe category cards for staggered animation
document.querySelectorAll('.category-card').forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(30px)';
    card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
    observer.observe(card);
});

// Parallax effect for hero background
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const heroImage = document.querySelector('.hero-image');
    if (heroImage) {
        heroImage.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
});

// Dynamic cart and wishlist counters
function updateCartCount() {
    const cartBadge = document.querySelector('.cart-btn .badge');
    const wishlistBadge = document.querySelector('.wishlist-btn .badge');
    
    // Simulate dynamic counts (in real app, this would come from state management)
    const cartCount = Math.floor(Math.random() * 10) + 1;
    const wishlistCount = Math.floor(Math.random() * 5) + 1;
    
    if (cartBadge) cartBadge.textContent = cartCount;
    if (wishlistBadge) wishlistBadge.textContent = wishlistCount;
}

// Category card hover effects
document.querySelectorAll('.category-card').forEach(card => {
    card.addEventListener('mouseenter', () => {
        card.style.zIndex = '10';
    });
    
    card.addEventListener('mouseleave', () => {
        card.style.zIndex = '1';
    });
});

// Search functionality
const searchInput = document.querySelector('.search-input');
if (searchInput) {
    searchInput.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();
        // In a real app, this would trigger search API calls
        console.log('Searching for:', query);
    });
    
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            const query = e.target.value.toLowerCase();
            // Redirect to search results page
            console.log('Search submitted:', query);
        }
    });
}

// Loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '1';
    
    // Animate hero content
    const heroContent = document.querySelector('.hero-content');
    if (heroContent) {
        heroContent.style.animation = 'fadeInUp 1s ease-out';
    }
    
    // Animate floating elements
    document.querySelectorAll('.floating-element').forEach((element, index) => {
        element.style.animationDelay = `${index}s`;
    });
});

// Resize handler
window.addEventListener('resize', () => {
    // Close mobile menu on resize
    if (window.innerWidth > 768) {
        mobileMenu.classList.remove('active');
        navToggle.classList.remove('active');
        searchBar.classList.remove('active');
        
        const spans = navToggle.querySelectorAll('span');
        spans[0].style.transform = 'none';
        spans[1].style.opacity = '1';
        spans[2].style.transform = 'none';
    }
});

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    // Set initial body opacity for loading animation
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.3s ease-in-out';

    // Update counters
    updateCartCount();

    // Add loading class to body
    document.body.classList.add('loading');

    // Remove loading class after a short delay
    setTimeout(() => {
        document.body.classList.remove('loading');
    }, 100);

    // Load AI Assistant
    loadAIAssistant();
});

// Load AI Assistant Widget
function loadAIAssistant() {
    // Check if AI assistant is already loaded
    if (document.getElementById('aiAssistant')) {
        return;
    }

    // Create AI assistant HTML
    const aiAssistantHTML = `
        <!-- AI Assistant Widget -->
        <div class="ai-assistant" id="aiAssistant">
            <div class="ai-toggle" id="aiToggle">
                <div class="ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ai-notification" id="aiNotification">1</div>
            </div>

            <div class="ai-chat-window" id="aiChatWindow">
                <div class="ai-header">
                    <div class="ai-info">
                        <div class="ai-avatar-large">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="ai-details">
                            <h3>Magna AI</h3>
                            <p class="ai-status">Online • Ready to help</p>
                        </div>
                    </div>
                    <div class="ai-actions">
                        <button class="ai-action-btn" id="aiMinimize">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button class="ai-action-btn" id="aiClose">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <div class="ai-chat-body" id="aiChatBody">
                    <div class="ai-welcome-message">
                        <div class="ai-message ai-message-bot">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <p>👋 Hi! I'm Magna AI, your personal shopping assistant. I can help you with:</p>
                                <ul>
                                    <li>🛍️ Finding the perfect products</li>
                                    <li>📏 Size recommendations</li>
                                    <li>🚚 Order tracking</li>
                                    <li>💬 General questions</li>
                                </ul>
                                <p>How can I assist you today?</p>
                            </div>
                        </div>
                    </div>

                    <div class="ai-quick-actions">
                        <button class="quick-action-btn" data-action="size-guide">
                            <i class="fas fa-ruler"></i>
                            Size Guide
                        </button>
                        <button class="quick-action-btn" data-action="track-order">
                            <i class="fas fa-truck"></i>
                            Track Order
                        </button>
                        <button class="quick-action-btn" data-action="recommendations">
                            <i class="fas fa-star"></i>
                            Get Recommendations
                        </button>
                        <button class="quick-action-btn" data-action="support">
                            <i class="fas fa-headset"></i>
                            Contact Support
                        </button>
                    </div>

                    <div class="ai-messages" id="aiMessages">
                        <!-- Chat messages will appear here -->
                    </div>
                </div>

                <div class="ai-input-area">
                    <div class="ai-typing-indicator" id="aiTyping">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span>Magna AI is typing...</span>
                    </div>

                    <div class="ai-input-container">
                        <input type="text" class="ai-input" id="aiInput" placeholder="Type your message...">
                        <button class="ai-send-btn" id="aiSendBtn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>

                    <div class="ai-suggestions" id="aiSuggestions">
                        <!-- Dynamic suggestions will appear here -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add AI assistant to body
    document.body.insertAdjacentHTML('beforeend', aiAssistantHTML);

    // Load AI assistant CSS
    loadAIAssistantCSS();

    // Load AI assistant JavaScript
    loadAIAssistantJS();
}

// Load AI Assistant CSS
function loadAIAssistantCSS() {
    if (document.getElementById('ai-assistant-css')) {
        return;
    }

    const style = document.createElement('style');
    style.id = 'ai-assistant-css';
    style.textContent = `
        .ai-assistant {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            font-family: var(--font-primary);
        }

        .ai-toggle {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(14, 165, 233, 0.3);
            transition: var(--transition-normal);
            position: relative;
        }

        .ai-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(14, 165, 233, 0.4);
        }

        .ai-avatar {
            color: white;
            font-size: 1.5rem;
        }

        .ai-notification {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: var(--accent-500);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .ai-chat-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 380px;
            height: 500px;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            display: none;
            flex-direction: column;
            overflow: hidden;
        }

        .ai-chat-window.active {
            display: flex;
        }

        .ai-header {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .ai-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .ai-avatar-large {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .ai-details h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .ai-status {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .ai-actions {
            display: flex;
            gap: 0.5rem;
        }

        .ai-action-btn {
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-fast);
        }

        .ai-action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .ai-chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
        }

        .ai-message {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .ai-message-user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .ai-message-bot .message-avatar {
            background: var(--primary-100);
            color: var(--primary-600);
        }

        .ai-message-user .message-avatar {
            background: var(--neutral-200);
            color: var(--neutral-600);
        }

        .message-content {
            background: var(--neutral-100);
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            max-width: 80%;
        }

        .ai-message-user .message-content {
            background: var(--primary-500);
            color: white;
        }

        .message-content p {
            margin-bottom: 0.5rem;
        }

        .message-content p:last-child {
            margin-bottom: 0;
        }

        .message-content ul {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }

        .ai-quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .quick-action-btn {
            padding: 0.75rem;
            background: var(--neutral-50);
            border: 1px solid var(--neutral-200);
            border-radius: 0.5rem;
            cursor: pointer;
            transition: var(--transition-fast);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--neutral-700);
        }

        .quick-action-btn:hover {
            background: var(--primary-50);
            border-color: var(--primary-200);
            color: var(--primary-700);
        }

        .ai-input-area {
            border-top: 1px solid var(--neutral-200);
            padding: 1rem;
        }

        .ai-typing-indicator {
            display: none;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: var(--neutral-500);
            font-size: 0.875rem;
        }

        .ai-typing-indicator.active {
            display: flex;
        }

        .typing-dots {
            display: flex;
            gap: 0.25rem;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            background: var(--neutral-400);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .ai-input-container {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .ai-input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 1px solid var(--neutral-300);
            border-radius: 1.5rem;
            outline: none;
            transition: var(--transition-fast);
        }

        .ai-input:focus {
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .ai-send-btn {
            width: 40px;
            height: 40px;
            background: var(--primary-500);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-fast);
        }

        .ai-send-btn:hover {
            background: var(--primary-600);
            transform: scale(1.05);
        }

        .ai-suggestions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
            flex-wrap: wrap;
        }

        .suggestion-btn {
            padding: 0.5rem 0.75rem;
            background: var(--neutral-100);
            border: 1px solid var(--neutral-200);
            border-radius: 1rem;
            font-size: 0.875rem;
            color: var(--neutral-700);
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .suggestion-btn:hover {
            background: var(--primary-100);
            border-color: var(--primary-300);
            color: var(--primary-700);
        }

        /* Mobile Responsive */
        @media (max-width: 480px) {
            .ai-chat-window {
                width: calc(100vw - 40px);
                height: 70vh;
                bottom: 80px;
                right: 20px;
                left: 20px;
            }

            .ai-quick-actions {
                grid-template-columns: 1fr;
            }
        }
    `;

    document.head.appendChild(style);
}

// Load AI Assistant JavaScript functionality
function loadAIAssistantJS() {
    // AI Assistant functionality
    const aiToggle = document.getElementById('aiToggle');
    const aiClose = document.getElementById('aiClose');
    const aiMinimize = document.getElementById('aiMinimize');
    const aiSendBtn = document.getElementById('aiSendBtn');
    const aiInput = document.getElementById('aiInput');
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');

    let isAIOpen = false;

    // Toggle AI chat
    if (aiToggle) {
        aiToggle.addEventListener('click', () => {
            const chatWindow = document.getElementById('aiChatWindow');
            const notification = document.getElementById('aiNotification');

            if (isAIOpen) {
                chatWindow.classList.remove('active');
                isAIOpen = false;
            } else {
                chatWindow.classList.add('active');
                notification.style.display = 'none';
                isAIOpen = true;

                // Focus input
                setTimeout(() => aiInput.focus(), 100);
            }
        });
    }

    // Close AI chat
    if (aiClose || aiMinimize) {
        [aiClose, aiMinimize].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', () => {
                    const chatWindow = document.getElementById('aiChatWindow');
                    chatWindow.classList.remove('active');
                    isAIOpen = false;
                });
            }
        });
    }

    // Send message
    function sendAIMessage() {
        const message = aiInput.value.trim();
        if (!message) return;

        // Add user message
        addAIMessage(message, 'user');
        aiInput.value = '';

        // Show typing indicator
        showAITyping();

        // Simulate AI response
        setTimeout(() => {
            hideAITyping();
            const response = getAIResponse(message);
            addAIMessage(response, 'bot');
        }, 1500);
    }

    if (aiSendBtn) {
        aiSendBtn.addEventListener('click', sendAIMessage);
    }

    if (aiInput) {
        aiInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendAIMessage();
            }
        });
    }

    // Quick actions
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const action = e.currentTarget.dataset.action;
            handleAIQuickAction(action);
        });
    });

    // Add message to chat
    function addAIMessage(text, sender) {
        const messagesContainer = document.getElementById('aiMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ai-message-${sender}`;

        const avatar = sender === 'bot' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';

        messageDiv.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <p>${text}</p>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Show typing indicator
    function showAITyping() {
        const typingIndicator = document.getElementById('aiTyping');
        typingIndicator.classList.add('active');
    }

    // Hide typing indicator
    function hideAITyping() {
        const typingIndicator = document.getElementById('aiTyping');
        typingIndicator.classList.remove('active');
    }

    // Get AI response
    function getAIResponse(message) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('size') || lowerMessage.includes('fit')) {
            return "I'd be happy to help you find the perfect size! Our size guide includes detailed measurements for all categories. Would you like me to show you the size chart for a specific item?";
        } else if (lowerMessage.includes('track') || lowerMessage.includes('order')) {
            return "I can help you track your order! Please provide your order number (format: M5-2024-XXXXXX) and I'll get the latest status for you.";
        } else if (lowerMessage.includes('recommend') || lowerMessage.includes('suggest')) {
            return "Based on your browsing history, I'd recommend our Urban Legend Hoodie and Midnight Essence perfume - they're perfect for your style!";
        } else if (lowerMessage.includes('payment') || lowerMessage.includes('mobile money')) {
            return "We accept all major payment methods including credit cards, PayPal, Apple Pay, Google Pay, and mobile money options like M-Pesa, MTN Mobile Money, and Airtel Money!";
        } else {
            return "Thanks for reaching out! I'm here to help with any questions about our products, orders, or services. What would you like to know?";
        }
    }

    // Handle quick actions
    function handleAIQuickAction(action) {
        switch (action) {
            case 'size-guide':
                addAIMessage("I'd like to see the size guide", 'user');
                showAITyping();
                setTimeout(() => {
                    hideAITyping();
                    addAIMessage("Here's our comprehensive size guide! It includes measurements for all our clothing categories.", 'bot');
                    window.open('size-guide.html', '_blank');
                }, 1000);
                break;

            case 'track-order':
                addAIMessage("I want to track my order", 'user');
                showAITyping();
                setTimeout(() => {
                    hideAITyping();
                    addAIMessage("I can help you track your order! Please provide your order number and email address.", 'bot');
                }, 1000);
                break;

            case 'recommendations':
                addAIMessage("Show me some recommendations", 'user');
                showAITyping();
                setTimeout(() => {
                    hideAITyping();
                    addAIMessage("Based on your browsing, I recommend our latest streetwear collection! The Urban Legend Hoodie and Street King T-Shirt are very popular.", 'bot');
                }, 1000);
                break;

            case 'support':
                addAIMessage("I need to contact support", 'user');
                showAITyping();
                setTimeout(() => {
                    hideAITyping();
                    addAIMessage("I'll connect you with our human support team. You can reach <NAME_EMAIL> or call +1 (234) 567-890.", 'bot');
                    window.open('contact.html', '_blank');
                }, 1000);
                break;
        }
    }

    // Show notification after 5 seconds
    setTimeout(() => {
        const notification = document.getElementById('aiNotification');
        if (notification && !isAIOpen) {
            notification.style.display = 'flex';
        }
    }, 5000);
}

// Performance optimization: Throttle scroll events
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply throttling to scroll events
const throttledScrollHandler = throttle(() => {
    const currentScrollY = window.scrollY;
    
    if (currentScrollY > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
    } else {
        header.style.background = 'rgba(255, 255, 255, 0.9)';
        header.style.boxShadow = 'none';
    }
}, 16);

window.addEventListener('scroll', throttledScrollHandler);
