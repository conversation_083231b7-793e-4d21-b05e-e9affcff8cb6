// Authentication System
class AuthSystem {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthState();
        this.loadUserData();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.auth-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabType = e.target.dataset.tab;
                this.switchTab(tabType);
            });
        });

        // Form submissions
        document.getElementById('signinFormElement')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSignIn(e.target);
        });

        document.getElementById('signupFormElement')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSignUp(e.target);
        });

        document.getElementById('forgotPasswordFormElement')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleForgotPassword(e.target);
        });

        // Password strength checker
        document.getElementById('signupPassword')?.addEventListener('input', (e) => {
            this.checkPasswordStrength(e.target.value);
        });

        // Dashboard navigation
        document.querySelectorAll('.dashboard-nav .nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.closest('.nav-item').dataset.section;
                if (section) {
                    this.showDashboardSection(section);
                }
            });
        });

        // Social auth buttons
        document.querySelector('.google-btn')?.addEventListener('click', () => {
            this.handleSocialAuth('google');
        });

        document.querySelector('.facebook-btn')?.addEventListener('click', () => {
            this.handleSocialAuth('facebook');
        });
    }

    switchTab(tabType) {
        // Update tab buttons
        document.querySelectorAll('.auth-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabType}"]`).classList.add('active');

        // Show corresponding form
        document.querySelectorAll('.auth-form-container').forEach(form => {
            form.classList.add('hidden');
        });

        if (tabType === 'signin') {
            document.getElementById('signinForm').classList.remove('hidden');
        } else if (tabType === 'signup') {
            document.getElementById('signupForm').classList.remove('hidden');
        }
    }

    async handleSignIn(form) {
        const formData = new FormData(form);
        const email = formData.get('email');
        const password = formData.get('password');
        const remember = formData.get('remember');

        try {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
            submitBtn.disabled = true;

            // Simulate API call
            await this.simulateApiCall();

            // Mock authentication
            const user = {
                id: 1,
                firstName: 'John',
                lastName: 'Doe',
                email: email,
                avatar: null,
                joinDate: '2024-01-15',
                orders: 12,
                wishlistItems: 8,
                totalSpent: 1247
            };

            this.setCurrentUser(user, remember);
            this.showDashboard();
            this.showNotification('Welcome back! You have been signed in successfully.', 'success');

        } catch (error) {
            this.showNotification('Invalid email or password. Please try again.', 'error');
        } finally {
            // Reset button
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    async handleSignUp(form) {
        const formData = new FormData(form);
        const firstName = formData.get('firstName');
        const lastName = formData.get('lastName');
        const email = formData.get('email');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        const terms = formData.get('terms');
        const newsletter = formData.get('newsletter');

        // Validation
        if (password !== confirmPassword) {
            this.showNotification('Passwords do not match.', 'error');
            return;
        }

        if (!terms) {
            this.showNotification('Please accept the Terms of Service.', 'error');
            return;
        }

        try {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';
            submitBtn.disabled = true;

            // Simulate API call
            await this.simulateApiCall();

            // Mock user creation
            const user = {
                id: Date.now(),
                firstName: firstName,
                lastName: lastName,
                email: email,
                avatar: null,
                joinDate: new Date().toISOString().split('T')[0],
                orders: 0,
                wishlistItems: 0,
                totalSpent: 0
            };

            this.setCurrentUser(user, true);
            this.showDashboard();
            this.showNotification('Account created successfully! Welcome to Magna5RRR.', 'success');

            // Send welcome email (simulated)
            this.sendWelcomeEmail(user);

        } catch (error) {
            this.showNotification('Failed to create account. Please try again.', 'error');
        } finally {
            // Reset button
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    async handleForgotPassword(form) {
        const formData = new FormData(form);
        const email = formData.get('email');

        try {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            submitBtn.disabled = true;

            // Simulate API call
            await this.simulateApiCall();

            // Simulate sending reset email
            this.sendPasswordResetEmail(email);
            this.showNotification(`Password reset link sent to ${email}. Please check your inbox.`, 'success');
            
            // Switch back to sign in
            setTimeout(() => {
                this.showSignIn();
            }, 2000);

        } catch (error) {
            this.showNotification('Failed to send reset email. Please try again.', 'error');
        } finally {
            // Reset button
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    handleSocialAuth(provider) {
        // Simulate social authentication
        this.showNotification(`${provider.charAt(0).toUpperCase() + provider.slice(1)} authentication would be implemented here.`, 'info');
        
        // Mock successful social auth
        setTimeout(() => {
            const user = {
                id: Date.now(),
                firstName: 'Social',
                lastName: 'User',
                email: `user@${provider}.com`,
                avatar: null,
                joinDate: new Date().toISOString().split('T')[0],
                orders: 0,
                wishlistItems: 0,
                totalSpent: 0
            };

            this.setCurrentUser(user, true);
            this.showDashboard();
            this.showNotification(`Signed in with ${provider} successfully!`, 'success');
        }, 1000);
    }

    checkPasswordStrength(password) {
        const strengthIndicator = document.getElementById('passwordStrength');
        if (!strengthIndicator) return;

        let strength = 0;
        
        // Length check
        if (password.length >= 8) strength++;
        
        // Uppercase check
        if (/[A-Z]/.test(password)) strength++;
        
        // Lowercase check
        if (/[a-z]/.test(password)) strength++;
        
        // Number check
        if (/\d/.test(password)) strength++;
        
        // Special character check
        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;

        // Update indicator
        strengthIndicator.className = 'password-strength';
        if (strength >= 2) strengthIndicator.classList.add('weak');
        if (strength >= 4) strengthIndicator.classList.add('medium');
        if (strength >= 5) strengthIndicator.classList.add('strong');
    }

    setCurrentUser(user, remember = false) {
        this.currentUser = user;
        
        // Store in localStorage or sessionStorage
        const storage = remember ? localStorage : sessionStorage;
        storage.setItem('magna5rrr_user', JSON.stringify(user));
        storage.setItem('magna5rrr_auth_token', 'mock_token_' + Date.now());
    }

    checkAuthState() {
        // Check for existing session
        const user = localStorage.getItem('magna5rrr_user') || sessionStorage.getItem('magna5rrr_user');
        const token = localStorage.getItem('magna5rrr_auth_token') || sessionStorage.getItem('magna5rrr_auth_token');
        
        if (user && token) {
            this.currentUser = JSON.parse(user);
            this.showDashboard();
        }
    }

    showDashboard() {
        document.getElementById('authContainer').classList.add('hidden');
        document.getElementById('dashboardContainer').classList.remove('hidden');
        
        // Update user info
        if (this.currentUser) {
            document.getElementById('userName').textContent = `${this.currentUser.firstName} ${this.currentUser.lastName}`;
            document.getElementById('userEmail').textContent = this.currentUser.email;
        }
        
        // Load dashboard data
        this.loadDashboardData();
    }

    showSignIn() {
        document.getElementById('forgotPasswordForm').classList.add('hidden');
        document.getElementById('signinForm').classList.remove('hidden');
        
        // Update tabs
        document.querySelectorAll('.auth-tab').forEach(tab => tab.classList.remove('active'));
        document.querySelector('[data-tab="signin"]').classList.add('active');
    }

    showDashboardSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.dashboard-nav .nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
        
        // Show section
        document.querySelectorAll('.dashboard-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}Section`).classList.add('active');
        
        // Load section data
        this.loadSectionData(sectionName);
    }

    loadDashboardData() {
        // Load recent orders
        this.loadRecentOrders();
    }

    loadRecentOrders() {
        const ordersList = document.getElementById('recentOrdersList');
        if (!ordersList) return;

        const mockOrders = [
            {
                id: '#M5-001',
                date: '2024-01-20',
                total: '$159.99',
                status: 'delivered',
                items: 'Urban Legend Hoodie, Tech Pro Backpack'
            },
            {
                id: '#M5-002',
                date: '2024-01-18',
                total: '$89.99',
                status: 'processing',
                items: 'Midnight Essence Perfume'
            },
            {
                id: '#M5-003',
                date: '2024-01-15',
                total: '$299.99',
                status: 'shipped',
                items: 'Royal Chain Necklace'
            }
        ];

        ordersList.innerHTML = mockOrders.map(order => `
            <div class="order-item">
                <div class="order-info">
                    <h4>${order.id}</h4>
                    <p>${order.items}</p>
                    <p>Ordered on ${order.date}</p>
                </div>
                <div class="order-details">
                    <div class="order-total">${order.total}</div>
                    <div class="order-status ${order.status}">${order.status}</div>
                </div>
            </div>
        `).join('');
    }

    loadSectionData(section) {
        // Load data based on section
        switch (section) {
            case 'orders':
                this.loadOrderHistory();
                break;
            case 'profile':
                this.loadProfileSettings();
                break;
            // Add more sections as needed
        }
    }

    signOut() {
        // Clear storage
        localStorage.removeItem('magna5rrr_user');
        localStorage.removeItem('magna5rrr_auth_token');
        sessionStorage.removeItem('magna5rrr_user');
        sessionStorage.removeItem('magna5rrr_auth_token');
        
        this.currentUser = null;
        
        // Show auth forms
        document.getElementById('dashboardContainer').classList.add('hidden');
        document.getElementById('authContainer').classList.remove('hidden');
        
        this.showNotification('You have been signed out successfully.', 'info');
    }

    // Utility methods
    async simulateApiCall() {
        return new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
    }

    sendWelcomeEmail(user) {
        console.log(`Welcome email sent to ${user.email}`);
        // In real implementation, this would call your email service
    }

    sendPasswordResetEmail(email) {
        console.log(`Password reset email sent to ${email}`);
        // In real implementation, this would call your email service
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Global functions
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function showForgotPassword() {
    document.getElementById('signinForm').classList.add('hidden');
    document.getElementById('forgotPasswordForm').classList.remove('hidden');
}

function showSignIn() {
    authSystem.showSignIn();
}

function signOut() {
    authSystem.signOut();
}

// Initialize authentication system
const authSystem = new AuthSystem();

// Add notification styles
const notificationStyles = `
<style>
.notification {
    position: fixed;
    top: 6rem;
    right: 1rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    z-index: 1000;
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    border-left: 4px solid #10b981;
}

.notification-error {
    border-left: 4px solid #ef4444;
}

.notification-info {
    border-left: 4px solid #3b82f6;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-content i {
    font-size: 1.25rem;
}

.notification-success i {
    color: #10b981;
}

.notification-error i {
    color: #ef4444;
}

.notification-info i {
    color: #3b82f6;
}

.notification-close {
    background: none;
    border: none;
    color: var(--neutral-400);
    cursor: pointer;
    padding: 0.25rem;
}

.notification-close:hover {
    color: var(--neutral-600);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', notificationStyles);
