/* Futuristic Neo-Urban Arcadia Theme System */

/* CSS Custom Properties for Theme System */
:root {
    /* Light Theme - Neo-Urban Day */
    --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --bg-secondary: rgba(255, 255, 255, 0.9);
    --bg-glass: rgba(255, 255, 255, 0.1);
    --bg-card: rgba(255, 255, 255, 0.8);
    
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-accent: #1e293b;
    
    --neon-primary: #00f5ff;
    --neon-secondary: #ff0080;
    --neon-accent: #39ff14;
    --neon-purple: #bf00ff;
    
    --glow-primary: 0 0 20px rgba(0, 245, 255, 0.5);
    --glow-secondary: 0 0 20px rgba(255, 0, 128, 0.5);
    --glow-accent: 0 0 20px rgba(57, 255, 20, 0.5);
    
    --gradient-neon: linear-gradient(45deg, var(--neon-primary), var(--neon-secondary));
    --gradient-cyber: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-street: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    
    --shadow-cyber: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-neon: 0 0 30px rgba(0, 245, 255, 0.3);
    
    --border-cyber: 1px solid rgba(0, 245, 255, 0.3);
    --border-glow: 2px solid transparent;
    
    /* Animation Variables */
    --float-duration: 6s;
    --pulse-duration: 2s;
    --glow-duration: 3s;
}

/* Dark Theme - Neo-Urban Night */
[data-theme="dark"] {
    --bg-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    --bg-secondary: rgba(16, 16, 35, 0.9);
    --bg-glass: rgba(0, 0, 0, 0.3);
    --bg-card: rgba(26, 26, 46, 0.8);
    
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-accent: #e2e8f0;
    
    --shadow-cyber: 0 8px 32px rgba(0, 0, 0, 0.5);
    --shadow-neon: 0 0 30px rgba(0, 245, 255, 0.5);
    
    --border-cyber: 1px solid rgba(0, 245, 255, 0.5);
}

/* Mood-Based Theme Variants */
[data-mood="energetic"] {
    --neon-primary: #ff0080;
    --neon-secondary: #00f5ff;
    --neon-accent: #ffff00;
    --gradient-mood: linear-gradient(45deg, #ff0080, #ffff00, #00f5ff);
}

[data-mood="calm"] {
    --neon-primary: #4ecdc4;
    --neon-secondary: #45b7d1;
    --neon-accent: #96ceb4;
    --gradient-mood: linear-gradient(45deg, #4ecdc4, #45b7d1, #96ceb4);
}

[data-mood="mysterious"] {
    --neon-primary: #bf00ff;
    --neon-secondary: #8000ff;
    --neon-accent: #4000ff;
    --gradient-mood: linear-gradient(45deg, #bf00ff, #8000ff, #4000ff);
}

/* Base Theme Application */
body {
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-x: hidden;
    position: relative;
}

/* Animated Background Elements */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(57, 255, 20, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -2;
    animation: backgroundPulse var(--pulse-duration) ease-in-out infinite alternate;
}

/* Floating Particles */
.cyber-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--neon-primary);
    border-radius: 50%;
    animation: float var(--float-duration) ease-in-out infinite;
    box-shadow: var(--glow-primary);
}

.particle:nth-child(2n) {
    background: var(--neon-secondary);
    box-shadow: var(--glow-secondary);
    animation-delay: -2s;
}

.particle:nth-child(3n) {
    background: var(--neon-accent);
    box-shadow: var(--glow-accent);
    animation-delay: -4s;
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 1001;
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: var(--border-cyber);
    border-radius: 50px;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-neon);
}

.theme-toggle:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--glow-primary);
}

.theme-controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.theme-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    position: relative;
    overflow: hidden;
}

.theme-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-neon);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.theme-btn:hover::before {
    opacity: 1;
}

.theme-btn i {
    position: relative;
    z-index: 1;
    color: var(--text-primary);
}

.theme-btn.active {
    background: var(--gradient-neon);
    box-shadow: var(--glow-primary);
    color: white;
}

/* Light Mode Button */
.light-mode-btn {
    background: linear-gradient(45deg, #ffd700, #ff8c00);
}

/* Dark Mode Button */
.dark-mode-btn {
    background: linear-gradient(45deg, #1a1a2e, #16213e);
}

/* Mood Selector */
.mood-selector {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mood-btn {
    width: 30px;
    height: 30px;
    margin: 0.25rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mood-energetic {
    background: linear-gradient(45deg, #ff0080, #ffff00);
}

.mood-calm {
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.mood-mysterious {
    background: linear-gradient(45deg, #bf00ff, #8000ff);
}

.mood-btn:hover,
.mood-btn.active {
    transform: scale(1.2);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

/* Enhanced Card Styles */
.cyber-card {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border: var(--border-cyber);
    border-radius: 20px;
    box-shadow: var(--shadow-cyber);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cyber-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.cyber-card:hover::before {
    left: 100%;
}

.cyber-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-neon);
    border-color: var(--neon-primary);
}

/* Neon Text Effects */
.neon-text {
    color: var(--neon-primary);
    text-shadow: 
        0 0 5px var(--neon-primary),
        0 0 10px var(--neon-primary),
        0 0 15px var(--neon-primary);
    animation: neonPulse var(--glow-duration) ease-in-out infinite alternate;
}

.neon-text-secondary {
    color: var(--neon-secondary);
    text-shadow: 
        0 0 5px var(--neon-secondary),
        0 0 10px var(--neon-secondary),
        0 0 15px var(--neon-secondary);
}

/* Cyber Buttons */
.cyber-btn {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: var(--border-cyber);
    color: var(--text-primary);
    padding: 1rem 2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.cyber-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-neon);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cyber-btn:hover::before {
    opacity: 1;
}

.cyber-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--glow-primary);
    color: white;
}

.cyber-btn span {
    position: relative;
    z-index: 1;
}

/* Floating Product Animation */
.floating-product {
    animation: productFloat var(--float-duration) ease-in-out infinite;
}

/* AI Graffiti Overlay */
.ai-graffiti {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    opacity: 0.1;
    background-image: 
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='10' y='30' font-family='Arial' font-size='8' fill='%2300f5ff' opacity='0.3'%3ENEO%3C/text%3E%3Ctext x='60' y='70' font-family='Arial' font-size='6' fill='%23ff0080' opacity='0.3'%3EURBAN%3C/text%3E%3C/svg%3E");
    animation: graffitiFloat 10s ease-in-out infinite;
}

/* Keyframe Animations */
@keyframes backgroundPulse {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) translateX(0px);
        opacity: 0.7;
    }
    25% { 
        transform: translateY(-20px) translateX(10px);
        opacity: 1;
    }
    50% { 
        transform: translateY(-10px) translateX(-5px);
        opacity: 0.8;
    }
    75% { 
        transform: translateY(-30px) translateX(15px);
        opacity: 1;
    }
}

@keyframes productFloat {
    0%, 100% { 
        transform: translateY(0px) rotateY(0deg);
    }
    50% { 
        transform: translateY(-20px) rotateY(180deg);
    }
}

@keyframes neonPulse {
    0% { 
        text-shadow: 
            0 0 5px var(--neon-primary),
            0 0 10px var(--neon-primary),
            0 0 15px var(--neon-primary);
    }
    100% { 
        text-shadow: 
            0 0 10px var(--neon-primary),
            0 0 20px var(--neon-primary),
            0 0 30px var(--neon-primary);
    }
}

@keyframes graffitiFloat {
    0%, 100% { 
        transform: translateX(0px);
        opacity: 0.1;
    }
    50% { 
        transform: translateX(20px);
        opacity: 0.2;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .theme-toggle {
        right: 10px;
        padding: 0.5rem;
    }
    
    .theme-btn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    .mood-btn {
        width: 25px;
        height: 25px;
    }
    
    .cyber-card {
        border-radius: 15px;
    }
    
    .cyber-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* AI Style Assistant */
.ai-style-assistant {
    position: fixed;
    bottom: 100px;
    right: 20px;
    z-index: 999;
}

.style-toggle {
    width: 60px;
    height: 60px;
    background: var(--gradient-neon);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-neon);
    transition: var(--transition-normal);
    position: relative;
}

.style-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--glow-primary);
}

.style-avatar {
    color: white;
    font-size: 1.5rem;
}

.style-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background: var(--neon-secondary);
    color: white;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    animation: pulse 2s infinite;
}

.style-panel {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 400px;
    max-height: 600px;
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border: var(--border-cyber);
    border-radius: 1rem;
    box-shadow: var(--shadow-cyber);
    display: none;
    flex-direction: column;
    overflow: hidden;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.style-panel.active {
    display: flex;
    transform: translateY(0);
    opacity: 1;
}

.style-header {
    background: var(--gradient-neon);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.style-header h3 {
    margin: 0;
    font-size: 1.125rem;
}

.mood-indicator {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0;
}

.close-style-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.style-content {
    padding: 1rem;
    overflow-y: auto;
    flex: 1;
}

.mood-section,
.recommendations-section,
.color-section {
    margin-bottom: 1.5rem;
}

.mood-section h4,
.recommendations-section h4,
.color-section h4 {
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.mood-options {
    display: flex;
    gap: 0.5rem;
}

.mood-option {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-cyber);
    background: var(--bg-glass);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    color: var(--text-primary);
}

.mood-option:hover,
.mood-option.active {
    background: var(--gradient-neon);
    color: white;
    transform: translateY(-2px);
}

.mood-option i {
    font-size: 1.25rem;
}

.mood-option span {
    font-size: 0.75rem;
    font-weight: 500;
}

.style-recommendations {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.recommendation-item {
    display: flex;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-glass);
    border: 1px solid var(--border-cyber);
    border-radius: 0.5rem;
    transition: var(--transition-fast);
}

.recommendation-item:hover {
    border-color: var(--neon-primary);
    box-shadow: var(--glow-primary);
}

.rec-image {
    width: 60px;
    height: 60px;
    border-radius: 0.5rem;
    overflow: hidden;
    flex-shrink: 0;
}

.rec-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.rec-info {
    flex: 1;
}

.rec-info h5 {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.rec-reason {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0 0 0.5rem 0;
}

.rec-actions {
    display: flex;
    gap: 0.5rem;
}

.rec-btn {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-cyber);
    background: var(--bg-glass);
    color: var(--text-primary);
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.75rem;
    transition: var(--transition-fast);
}

.rec-btn:hover {
    background: var(--neon-primary);
    color: white;
}

.color-palette {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    transition: var(--transition-fast);
    border: 2px solid var(--border-cyber);
}

.color-swatch:hover {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.color-code {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.6rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.75rem;
    background: var(--bg-glass);
    border: 1px solid var(--border-cyber);
    color: var(--text-primary);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.action-btn:hover {
    background: var(--gradient-neon);
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design for Style Assistant */
@media (max-width: 480px) {
    .style-panel {
        width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
    }

    .mood-options {
        flex-direction: column;
    }

    .color-palette {
        justify-content: center;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-cyber: 2px solid var(--neon-primary);
        --shadow-neon: 0 0 10px var(--neon-primary);
    }
}
