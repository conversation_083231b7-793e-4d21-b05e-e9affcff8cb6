<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Checkout - Magna5RRR</title>
    <meta name="description" content="Complete your purchase securely with multiple payment options including mobile money.">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="checkout.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <!-- Logo -->
                <div class="nav-logo">
                    <a href="index.html" class="logo-link">
                        <div class="logo-icon">M5</div>
                        <span class="logo-text">Magna5RRR</span>
                    </a>
                </div>

                <!-- Checkout Progress -->
                <div class="checkout-progress">
                    <div class="progress-step active">
                        <div class="step-number">1</div>
                        <span>Cart</span>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step active">
                        <div class="step-number">2</div>
                        <span>Checkout</span>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step">
                        <div class="step-number">3</div>
                        <span>Payment</span>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step">
                        <div class="step-number">4</div>
                        <span>Complete</span>
                    </div>
                </div>

                <!-- Security Badge -->
                <div class="security-badge">
                    <i class="fas fa-shield-alt"></i>
                    <span>Secure Checkout</span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Checkout Section -->
    <section class="checkout-section">
        <div class="container">
            <div class="checkout-layout">
                <!-- Checkout Form -->
                <div class="checkout-form">
                    <form id="checkoutForm" class="multi-step-form">
                        <!-- Step 1: Shipping Information -->
                        <div class="form-step active" id="step1">
                            <div class="step-header">
                                <h2>Shipping Information</h2>
                                <p>Where should we deliver your order?</p>
                            </div>

                            <div class="form-section">
                                <h3>Contact Information</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="email">Email Address *</label>
                                        <input type="email" id="email" name="email" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="phone">Phone Number *</label>
                                        <input type="tel" id="phone" name="phone" required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Shipping Address</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="firstName">First Name *</label>
                                        <input type="text" id="firstName" name="firstName" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="lastName">Last Name *</label>
                                        <input type="text" id="lastName" name="lastName" required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="address">Street Address *</label>
                                    <input type="text" id="address" name="address" placeholder="123 Main Street" required>
                                </div>

                                <div class="form-group">
                                    <label for="apartment">Apartment, suite, etc. (optional)</label>
                                    <input type="text" id="apartment" name="apartment" placeholder="Apt 4B">
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="city">City *</label>
                                        <input type="text" id="city" name="city" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="state">State/Province *</label>
                                        <input type="text" id="state" name="state" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="zipCode">ZIP/Postal Code *</label>
                                        <input type="text" id="zipCode" name="zipCode" required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="country">Country *</label>
                                    <select id="country" name="country" required>
                                        <option value="">Select Country</option>
                                        <option value="US">United States</option>
                                        <option value="CA">Canada</option>
                                        <option value="UK">United Kingdom</option>
                                        <option value="KE">Kenya</option>
                                        <option value="UG">Uganda</option>
                                        <option value="TZ">Tanzania</option>
                                        <option value="GH">Ghana</option>
                                        <option value="NG">Nigeria</option>
                                        <option value="ZA">South Africa</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>Shipping Method</h3>
                                <div class="shipping-options">
                                    <label class="shipping-option">
                                        <input type="radio" name="shipping" value="standard" checked>
                                        <div class="option-content">
                                            <div class="option-info">
                                                <h4>Standard Shipping</h4>
                                                <p>5-7 business days</p>
                                            </div>
                                            <div class="option-price">Free</div>
                                        </div>
                                    </label>

                                    <label class="shipping-option">
                                        <input type="radio" name="shipping" value="express">
                                        <div class="option-content">
                                            <div class="option-info">
                                                <h4>Express Shipping</h4>
                                                <p>2-3 business days</p>
                                            </div>
                                            <div class="option-price">$9.99</div>
                                        </div>
                                    </label>

                                    <label class="shipping-option">
                                        <input type="radio" name="shipping" value="overnight">
                                        <div class="option-content">
                                            <div class="option-info">
                                                <h4>Overnight Shipping</h4>
                                                <p>Next business day</p>
                                            </div>
                                            <div class="option-price">$24.99</div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="step-actions">
                                <button type="button" class="btn btn-primary next-step">
                                    Continue to Payment
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: Payment Information -->
                        <div class="form-step" id="step2">
                            <div class="step-header">
                                <h2>Payment Information</h2>
                                <p>Choose your preferred payment method</p>
                            </div>

                            <div class="payment-methods">
                                <!-- Credit/Debit Cards -->
                                <div class="payment-method active" data-method="card">
                                    <div class="method-header">
                                        <input type="radio" name="paymentMethod" value="card" checked>
                                        <h3>Credit/Debit Card</h3>
                                        <div class="card-icons">
                                            <i class="fab fa-cc-visa"></i>
                                            <i class="fab fa-cc-mastercard"></i>
                                            <i class="fab fa-cc-amex"></i>
                                        </div>
                                    </div>
                                    <div class="method-content">
                                        <div class="form-group">
                                            <label for="cardNumber">Card Number *</label>
                                            <input type="text" id="cardNumber" name="cardNumber" placeholder="1234 5678 9012 3456" maxlength="19">
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="expiryDate">Expiry Date *</label>
                                                <input type="text" id="expiryDate" name="expiryDate" placeholder="MM/YY" maxlength="5">
                                            </div>
                                            <div class="form-group">
                                                <label for="cvv">CVV *</label>
                                                <input type="text" id="cvv" name="cvv" placeholder="123" maxlength="4">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="cardName">Name on Card *</label>
                                            <input type="text" id="cardName" name="cardName" placeholder="John Doe">
                                        </div>
                                    </div>
                                </div>

                                <!-- Mobile Money -->
                                <div class="payment-method" data-method="mobile">
                                    <div class="method-header">
                                        <input type="radio" name="paymentMethod" value="mobile">
                                        <h3>Mobile Money</h3>
                                        <div class="mobile-icons">
                                            <img src="https://upload.wikimedia.org/wikipedia/commons/1/15/M-PESA_LOGO-01.svg" alt="M-Pesa" style="height: 20px;">
                                            <span class="mobile-provider">MTN</span>
                                            <span class="mobile-provider">Airtel</span>
                                        </div>
                                    </div>
                                    <div class="method-content">
                                        <div class="form-group">
                                            <label for="mobileProvider">Mobile Money Provider *</label>
                                            <select id="mobileProvider" name="mobileProvider">
                                                <option value="">Select Provider</option>
                                                <option value="mpesa">M-Pesa (Kenya)</option>
                                                <option value="mtn">MTN Mobile Money</option>
                                                <option value="airtel">Airtel Money</option>
                                                <option value="tigo">Tigo Pesa</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="mobileNumber">Mobile Number *</label>
                                            <input type="tel" id="mobileNumber" name="mobileNumber" placeholder="+254 700 000 000">
                                        </div>
                                        <div class="mobile-info">
                                            <i class="fas fa-info-circle"></i>
                                            <p>You will receive a payment prompt on your mobile device to complete the transaction.</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- PayPal -->
                                <div class="payment-method" data-method="paypal">
                                    <div class="method-header">
                                        <input type="radio" name="paymentMethod" value="paypal">
                                        <h3>PayPal</h3>
                                        <i class="fab fa-paypal"></i>
                                    </div>
                                    <div class="method-content">
                                        <div class="paypal-info">
                                            <i class="fab fa-paypal"></i>
                                            <p>You will be redirected to PayPal to complete your payment securely.</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Digital Wallets -->
                                <div class="payment-method" data-method="wallet">
                                    <div class="method-header">
                                        <input type="radio" name="paymentMethod" value="wallet">
                                        <h3>Digital Wallet</h3>
                                        <div class="wallet-icons">
                                            <i class="fab fa-apple-pay"></i>
                                            <i class="fab fa-google-pay"></i>
                                        </div>
                                    </div>
                                    <div class="method-content">
                                        <div class="wallet-options">
                                            <button type="button" class="wallet-btn apple-pay">
                                                <i class="fab fa-apple-pay"></i>
                                                Pay with Apple Pay
                                            </button>
                                            <button type="button" class="wallet-btn google-pay">
                                                <i class="fab fa-google-pay"></i>
                                                Pay with Google Pay
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="step-actions">
                                <button type="button" class="btn btn-outline prev-step">
                                    <i class="fas fa-arrow-left"></i>
                                    Back to Shipping
                                </button>
                                <button type="button" class="btn btn-primary next-step">
                                    Review Order
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Order Review -->
                        <div class="form-step" id="step3">
                            <div class="step-header">
                                <h2>Review Your Order</h2>
                                <p>Please review your order details before completing your purchase</p>
                            </div>

                            <div class="order-review">
                                <div class="review-section">
                                    <h3>Shipping Address</h3>
                                    <div class="review-content" id="reviewShipping">
                                        <!-- Shipping details will be populated here -->
                                    </div>
                                    <button type="button" class="edit-btn" data-step="1">Edit</button>
                                </div>

                                <div class="review-section">
                                    <h3>Payment Method</h3>
                                    <div class="review-content" id="reviewPayment">
                                        <!-- Payment details will be populated here -->
                                    </div>
                                    <button type="button" class="edit-btn" data-step="2">Edit</button>
                                </div>

                                <div class="review-section">
                                    <h3>Order Items</h3>
                                    <div class="review-content" id="reviewItems">
                                        <!-- Order items will be populated here -->
                                    </div>
                                </div>
                            </div>

                            <div class="terms-section">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="terms" required>
                                    <span class="checkmark"></span>
                                    I agree to the <a href="terms.html" target="_blank">Terms of Service</a> and <a href="privacy.html" target="_blank">Privacy Policy</a>
                                </label>
                            </div>

                            <div class="step-actions">
                                <button type="button" class="btn btn-outline prev-step">
                                    <i class="fas fa-arrow-left"></i>
                                    Back to Payment
                                </button>
                                <button type="submit" class="btn btn-primary place-order-btn">
                                    <i class="fas fa-lock"></i>
                                    Place Order
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Order Summary -->
                <div class="order-summary">
                    <div class="summary-card">
                        <h3>Order Summary</h3>
                        
                        <div class="order-items" id="orderItems">
                            <!-- Items will be loaded from cart -->
                        </div>

                        <div class="summary-calculations">
                            <div class="summary-row">
                                <span>Subtotal</span>
                                <span id="subtotal">$319.97</span>
                            </div>
                            <div class="summary-row">
                                <span>Shipping</span>
                                <span id="shippingCost">Free</span>
                            </div>
                            <div class="summary-row">
                                <span>Tax</span>
                                <span id="tax">$25.60</span>
                            </div>
                            <div class="summary-divider"></div>
                            <div class="summary-row total">
                                <span>Total</span>
                                <span id="total">$345.57</span>
                            </div>
                        </div>

                        <div class="promo-section">
                            <div class="promo-input-group">
                                <input type="text" placeholder="Promo code" class="promo-input" id="promoCode">
                                <button type="button" class="promo-btn" id="applyPromo">Apply</button>
                            </div>
                        </div>

                        <div class="security-features">
                            <div class="security-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>SSL Encrypted</span>
                            </div>
                            <div class="security-item">
                                <i class="fas fa-lock"></i>
                                <span>Secure Payment</span>
                            </div>
                            <div class="security-item">
                                <i class="fas fa-undo"></i>
                                <span>30-Day Returns</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="script.js"></script>
    <script src="checkout.js"></script>
</body>
</html>
