// Essentials Data
const essentialsProducts = [
    {
        id: 1,
        name: "Tech Pro Backpack",
        category: "bags",
        brand: "magna5rrr",
        price: 79.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 203,
        colors: ["black", "gray"],
        badge: "bestseller",
        inStock: true,
        description: "Premium laptop backpack with multiple compartments"
    },
    {
        id: 2,
        name: "Wireless Charging Pad",
        category: "tech",
        brand: "premium",
        price: 45.99,
        originalPrice: 59.99,
        image: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 156,
        colors: ["black", "white"],
        badge: "new",
        inStock: true,
        description: "Fast wireless charging for all compatible devices"
    },
    {
        id: 3,
        name: "Premium Water Bottle",
        category: "lifestyle",
        brand: "magna5rrr",
        price: 35.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1602143407151-7111542de6e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 89,
        colors: ["black", "white", "blue"],
        badge: null,
        inStock: true,
        description: "Insulated stainless steel water bottle"
    },
    {
        id: 4,
        name: "Travel Organizer Set",
        category: "travel",
        brand: "limited",
        price: 129.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 67,
        colors: ["black", "brown"],
        badge: "limited",
        inStock: true,
        description: "Complete travel organization system"
    },
    {
        id: 5,
        name: "Bluetooth Speaker",
        category: "tech",
        brand: "premium",
        price: 89.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 5,
        reviews: 234,
        colors: ["black", "white", "red"],
        badge: "bestseller",
        inStock: true,
        description: "Portable speaker with premium sound quality"
    },
    {
        id: 6,
        name: "Minimalist Wallet",
        category: "lifestyle",
        brand: "magna5rrr",
        price: 49.99,
        originalPrice: 69.99,
        image: "https://images.unsplash.com/photo-1627123424574-724758594e93?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 145,
        colors: ["black", "brown"],
        badge: null,
        inStock: true,
        description: "Slim leather wallet with RFID protection"
    },
    {
        id: 7,
        name: "Gym Duffle Bag",
        category: "bags",
        brand: "magna5rrr",
        price: 69.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 98,
        colors: ["black", "gray", "blue"],
        badge: null,
        inStock: true,
        description: "Spacious duffle bag for gym and travel"
    },
    {
        id: 8,
        name: "Phone Stand & Charger",
        category: "tech",
        brand: "premium",
        price: 29.99,
        originalPrice: null,
        image: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        rating: 4,
        reviews: 76,
        colors: ["black", "white"],
        badge: "new",
        inStock: true,
        description: "Adjustable phone stand with wireless charging"
    }
];

// DOM Elements
const productsGrid = document.getElementById('productsGrid');
const filterToggle = document.getElementById('filterToggle');
const filterPanel = document.getElementById('filterPanel');
const sortSelect = document.getElementById('sortSelect');
const loadMoreBtn = document.getElementById('loadMoreBtn');
const priceRange = document.getElementById('priceRange');
const priceValue = document.getElementById('priceValue');

// State
let currentProducts = [...essentialsProducts];
let displayedProducts = 6;
let filters = {
    categories: ['bags'],
    brands: [],
    priceMax: 150,
    sortBy: 'featured'
};

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    renderProducts();
    setupEventListeners();
    updateFiltersFromState();
});

// Event Listeners
function setupEventListeners() {
    // Filter toggle
    filterToggle.addEventListener('click', () => {
        filterPanel.classList.toggle('active');
        filterToggle.classList.toggle('active');
    });

    // Sort select
    sortSelect.addEventListener('change', (e) => {
        filters.sortBy = e.target.value;
        applyFilters();
    });

    // Price range
    if (priceRange) {
        priceRange.addEventListener('input', (e) => {
            const value = e.target.value;
            priceValue.textContent = `$${value}`;
            filters.priceMax = parseInt(value);
        });

        priceRange.addEventListener('change', () => {
            applyFilters();
        });
    }

    // Category checkboxes
    document.querySelectorAll('input[name="category"]').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const category = e.target.value;
            if (e.target.checked) {
                if (!filters.categories.includes(category)) {
                    filters.categories.push(category);
                }
            } else {
                filters.categories = filters.categories.filter(c => c !== category);
            }
            applyFilters();
        });
    });

    // Brand checkboxes
    document.querySelectorAll('input[name="brand"]').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const brand = e.target.value;
            if (e.target.checked) {
                if (!filters.brands.includes(brand)) {
                    filters.brands.push(brand);
                }
            } else {
                filters.brands = filters.brands.filter(b => b !== brand);
            }
            applyFilters();
        });
    });

    // Clear filters
    document.querySelector('.clear-filters').addEventListener('click', () => {
        clearAllFilters();
    });

    // Apply filters
    document.querySelector('.apply-filters').addEventListener('click', () => {
        applyFilters();
        filterPanel.classList.remove('active');
        filterToggle.classList.remove('active');
    });

    // Load more
    loadMoreBtn.addEventListener('click', () => {
        displayedProducts += 6;
        renderProducts();
    });
}

// Update filters UI from state
function updateFiltersFromState() {
    // Update category checkboxes
    document.querySelectorAll('input[name="category"]').forEach(checkbox => {
        checkbox.checked = filters.categories.includes(checkbox.value);
    });

    // Update price range
    if (priceRange) {
        priceRange.value = filters.priceMax;
        priceValue.textContent = `$${filters.priceMax}`;
    }
}

// Apply filters and sorting
function applyFilters() {
    let filtered = [...essentialsProducts];

    // Filter by categories
    if (filters.categories.length > 0) {
        filtered = filtered.filter(product => 
            filters.categories.includes(product.category)
        );
    }

    // Filter by brands
    if (filters.brands.length > 0) {
        filtered = filtered.filter(product => 
            filters.brands.includes(product.brand)
        );
    }

    // Filter by price
    filtered = filtered.filter(product => product.price <= filters.priceMax);

    // Sort products
    switch (filters.sortBy) {
        case 'newest':
            filtered.sort((a, b) => b.id - a.id);
            break;
        case 'price-low':
            filtered.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            filtered.sort((a, b) => b.price - a.price);
            break;
        case 'rating':
            filtered.sort((a, b) => b.rating - a.rating);
            break;
        default:
            // Featured - keep original order
            break;
    }

    currentProducts = filtered;
    displayedProducts = 6;
    renderProducts();
    updateResultsCount();
}

// Clear all filters
function clearAllFilters() {
    filters = {
        categories: [],
        brands: [],
        priceMax: 300,
        sortBy: 'featured'
    };

    // Update UI
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });

    if (priceRange) {
        priceRange.value = 300;
        priceValue.textContent = '$300';
    }

    sortSelect.value = 'featured';

    applyFilters();
}

// Update results count
function updateResultsCount() {
    const resultsCount = document.querySelector('.results-count span');
    const showing = Math.min(displayedProducts, currentProducts.length);
    resultsCount.textContent = `Showing ${showing} of ${currentProducts.length} essentials`;
}

// Render products
function renderProducts() {
    const productsToShow = currentProducts.slice(0, displayedProducts);
    
    productsGrid.innerHTML = productsToShow.map(product => `
        <div class="product-card" data-product-id="${product.id}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" loading="lazy">
                <div class="product-overlay">
                    <button class="quick-view-btn" onclick="quickView(${product.id})">
                        <i class="fas fa-eye"></i>
                        Quick View
                    </button>
                    <button class="add-to-cart-btn" onclick="addToCart(${product.id})">
                        <i class="fas fa-shopping-cart"></i>
                        Add to Cart
                    </button>
                    <button class="wishlist-btn-product" onclick="toggleWishlist(${product.id})">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>
                ${product.badge ? `<span class="product-badge ${product.badge}">${product.badge}</span>` : ''}
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-category">${formatCategory(product.category)} • ${formatBrand(product.brand)}</p>
                <div class="product-price">
                    <span class="current-price">$${product.price}</span>
                    ${product.originalPrice ? `<span class="original-price">$${product.originalPrice}</span>` : ''}
                </div>
                <div class="product-rating">
                    <div class="stars">
                        ${generateStars(product.rating)}
                    </div>
                    <span class="rating-count">(${product.reviews})</span>
                </div>
            </div>
        </div>
    `).join('');

    // Show/hide load more button
    if (displayedProducts >= currentProducts.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'inline-flex';
    }

    updateResultsCount();
}

// Helper functions
function formatCategory(category) {
    const categoryMap = {
        'bags': 'Bags & Backpacks',
        'tech': 'Tech Accessories',
        'lifestyle': 'Lifestyle',
        'travel': 'Travel'
    };
    return categoryMap[category] || category;
}

function formatBrand(brand) {
    const brandMap = {
        'magna5rrr': 'Magna5RRR',
        'premium': 'Premium',
        'limited': 'Limited Edition'
    };
    return brandMap[brand] || brand;
}

// Generate star rating HTML
function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    return stars;
}

// Product actions
function quickView(productId) {
    const product = essentialsProducts.find(p => p.id === productId);
    alert(`Quick view for: ${product.name}\nCategory: ${formatCategory(product.category)}\nBrand: ${formatBrand(product.brand)}\nPrice: $${product.price}\nDescription: ${product.description}`);
}

function addToCart(productId) {
    const product = essentialsProducts.find(p => p.id === productId);
    alert(`Added "${product.name}" to cart!`);
    
    // Update cart badge
    const cartBadge = document.querySelector('.cart-btn .badge');
    if (cartBadge) {
        const currentCount = parseInt(cartBadge.textContent);
        cartBadge.textContent = currentCount + 1;
    }
}

function toggleWishlist(productId) {
    const product = essentialsProducts.find(p => p.id === productId);
    alert(`Toggled wishlist for: ${product.name}`);
    
    // Update wishlist badge
    const wishlistBadge = document.querySelector('.wishlist-btn .badge');
    if (wishlistBadge) {
        const currentCount = parseInt(wishlistBadge.textContent);
        wishlistBadge.textContent = Math.max(0, currentCount + (Math.random() > 0.5 ? 1 : -1));
    }
}
