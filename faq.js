// FAQ Page Functionality
class FAQSystem {
    constructor() {
        this.setupEventListeners();
        this.setupSearch();
    }

    setupEventListeners() {
        // FAQ item toggles
        document.querySelectorAll('.faq-item').forEach(item => {
            const question = item.querySelector('.faq-question');
            question.addEventListener('click', () => this.toggleFAQ(item));
        });

        // Category filters
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.filterByCategory(e.target.dataset.category));
        });

        // Search functionality
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.searchFAQs(e.target.value));
        }
    }

    toggleFAQ(item) {
        const answer = item.querySelector('.faq-answer');
        const icon = item.querySelector('.faq-question i');
        const isOpen = item.classList.contains('active');

        if (isOpen) {
            item.classList.remove('active');
            answer.style.maxHeight = '0';
            icon.style.transform = 'rotate(0deg)';
        } else {
            // Close other open FAQs
            document.querySelectorAll('.faq-item.active').forEach(openItem => {
                if (openItem !== item) {
                    openItem.classList.remove('active');
                    openItem.querySelector('.faq-answer').style.maxHeight = '0';
                    openItem.querySelector('.faq-question i').style.transform = 'rotate(0deg)';
                }
            });

            item.classList.add('active');
            answer.style.maxHeight = answer.scrollHeight + 'px';
            icon.style.transform = 'rotate(180deg)';
        }

        // Track FAQ interaction
        if (window.magnaAI) {
            window.magnaAI.logActivity('faq_interaction', {
                question: item.querySelector('.faq-question h3').textContent,
                action: isOpen ? 'close' : 'open'
            });
        }
    }

    filterByCategory(category) {
        // Update active category button
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // Filter FAQ items
        document.querySelectorAll('.faq-item').forEach(item => {
            if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
                // Close if open
                item.classList.remove('active');
                item.querySelector('.faq-answer').style.maxHeight = '0';
                item.querySelector('.faq-question i').style.transform = 'rotate(0deg)';
            }
        });

        // Track category filter
        if (window.magnaAI) {
            window.magnaAI.logActivity('faq_filter', { category: category });
        }
    }

    searchFAQs(query) {
        const searchTerm = query.toLowerCase().trim();
        
        if (searchTerm === '') {
            // Show all items
            document.querySelectorAll('.faq-item').forEach(item => {
                item.style.display = 'block';
                this.removeHighlight(item);
            });
            return;
        }

        document.querySelectorAll('.faq-item').forEach(item => {
            const question = item.querySelector('.faq-question h3').textContent.toLowerCase();
            const answer = item.querySelector('.faq-answer').textContent.toLowerCase();
            
            if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                item.style.display = 'block';
                this.highlightSearchTerm(item, searchTerm);
            } else {
                item.style.display = 'none';
                this.removeHighlight(item);
            }
        });

        // Track search
        if (window.magnaAI) {
            window.magnaAI.logActivity('faq_search', { query: searchTerm });
        }
    }

    highlightSearchTerm(item, term) {
        const question = item.querySelector('.faq-question h3');
        const answer = item.querySelector('.faq-answer');
        
        // Remove existing highlights
        this.removeHighlight(item);
        
        // Highlight in question
        const questionText = question.textContent;
        const highlightedQuestion = this.addHighlight(questionText, term);
        question.innerHTML = highlightedQuestion;
        
        // Highlight in answer
        const answerText = answer.innerHTML;
        const highlightedAnswer = this.addHighlight(answerText, term);
        answer.innerHTML = highlightedAnswer;
    }

    removeHighlight(item) {
        const highlights = item.querySelectorAll('.highlight');
        highlights.forEach(highlight => {
            const parent = highlight.parentNode;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize();
        });
    }

    addHighlight(text, term) {
        const regex = new RegExp(`(${term})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    }

    setupSearch() {
        // Auto-focus search on page load if there's a search parameter
        const urlParams = new URLSearchParams(window.location.search);
        const searchQuery = urlParams.get('search');
        
        if (searchQuery) {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = searchQuery;
                this.searchFAQs(searchQuery);
            }
        }
    }
}

// Initialize FAQ system
document.addEventListener('DOMContentLoaded', () => {
    new FAQSystem();
});

// Add FAQ-specific styles
const style = document.createElement('style');
style.textContent = `
    .faq-section {
        padding: 3rem 0;
        background: var(--neutral-50);
    }

    .faq-categories {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 3rem;
        justify-content: center;
    }

    .category-btn {
        padding: 0.75rem 1.5rem;
        background: white;
        border: 1px solid var(--neutral-300);
        border-radius: 2rem;
        color: var(--neutral-600);
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition-fast);
    }

    .category-btn:hover {
        border-color: var(--primary-400);
        color: var(--primary-600);
    }

    .category-btn.active {
        background: var(--primary-500);
        border-color: var(--primary-500);
        color: white;
    }

    .faq-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .faq-item {
        background: white;
        border-radius: 0.75rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: var(--transition-normal);
    }

    .faq-item:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .faq-question {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        cursor: pointer;
        transition: var(--transition-fast);
    }

    .faq-question:hover {
        background: var(--neutral-50);
    }

    .faq-question h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--neutral-900);
        margin: 0;
        flex: 1;
    }

    .faq-question i {
        color: var(--primary-500);
        transition: var(--transition-fast);
        font-size: 1rem;
    }

    .faq-answer {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
        background: var(--neutral-50);
    }

    .faq-answer p,
    .faq-answer ul,
    .faq-answer ol {
        padding: 0 1.5rem 1.5rem;
        margin: 0;
        color: var(--neutral-700);
        line-height: 1.6;
    }

    .faq-answer ul,
    .faq-answer ol {
        padding-left: 3rem;
    }

    .faq-answer li {
        margin-bottom: 0.5rem;
    }

    .faq-answer a {
        color: var(--primary-600);
        text-decoration: none;
    }

    .faq-answer a:hover {
        text-decoration: underline;
    }

    .highlight {
        background: var(--primary-200);
        color: var(--primary-800);
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-weight: 600;
    }

    .help-section {
        text-align: center;
        margin-top: 4rem;
        padding: 3rem;
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .help-section h2 {
        font-size: 1.75rem;
        font-weight: 600;
        color: var(--neutral-900);
        margin-bottom: 1rem;
    }

    .help-section p {
        color: var(--neutral-600);
        margin-bottom: 2rem;
    }

    .help-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .help-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        padding: 2rem;
        background: var(--neutral-50);
        border: 1px solid var(--neutral-200);
        border-radius: 0.75rem;
        text-decoration: none;
        color: inherit;
        transition: var(--transition-normal);
        cursor: pointer;
    }

    .help-option:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-300);
    }

    .help-option i {
        font-size: 2rem;
        color: var(--primary-500);
    }

    .help-option h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--neutral-900);
        margin: 0;
    }

    .help-option p {
        color: var(--neutral-600);
        font-size: 0.875rem;
        margin: 0;
    }

    @media (max-width: 768px) {
        .faq-categories {
            justify-content: flex-start;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }

        .category-btn {
            white-space: nowrap;
            flex-shrink: 0;
        }

        .faq-question {
            padding: 1rem;
        }

        .faq-question h3 {
            font-size: 1rem;
        }

        .faq-answer p,
        .faq-answer ul,
        .faq-answer ol {
            padding: 0 1rem 1rem;
        }

        .help-section {
            padding: 2rem 1rem;
        }

        .help-options {
            grid-template-columns: 1fr;
        }
    }
`;
document.head.appendChild(style);
