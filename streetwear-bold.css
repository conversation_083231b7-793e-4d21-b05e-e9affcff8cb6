/* Bold Aggressive Streetwear Theme */

:root {
    /* Bold Streetwear Color Palette */
    --street-black: #0a0a0a;
    --street-charcoal: #1a1a1a;
    --street-concrete: #2a2a2a;
    --street-steel: #3a3a3a;
    --street-white: #f8f8f8;
    --street-off-white: #e8e8e8;
    
    /* Aggressive Accent Colors */
    --street-red: #ff0000;
    --street-orange: #ff4500;
    --street-electric: #00ffff;
    --street-lime: #32ff32;
    --street-purple: #8a2be2;
    --street-gold: #ffd700;
    
    /* Typography */
    --font-street: 'Inter', 'Helvetica Neue', Arial, sans-serif;
    --font-display: 'Playfair Display', Georgia, serif;
    --font-mono: 'JetBrains Mono', 'Courier New', monospace;
    
    /* Shadows & Effects */
    --shadow-harsh: 0 4px 0 var(--street-charcoal);
    --shadow-deep: 0 8px 24px rgba(0, 0, 0, 0.8);
    --shadow-glow: 0 0 20px;
    
    /* Transitions */
    --transition-snap: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Light Theme - Urban Day */
[data-theme="light"] {
    --bg-primary: linear-gradient(135deg, var(--street-white) 0%, var(--street-off-white) 100%);
    --bg-secondary: var(--street-white);
    --bg-card: rgba(255, 255, 255, 0.95);
    --bg-overlay: rgba(0, 0, 0, 0.1);
    
    --text-primary: var(--street-black);
    --text-secondary: var(--street-charcoal);
    --text-muted: var(--street-concrete);
    
    --border-primary: var(--street-concrete);
    --border-accent: var(--street-red);
}

/* Dark Theme - Urban Night */
[data-theme="dark"] {
    --bg-primary: linear-gradient(135deg, var(--street-black) 0%, var(--street-charcoal) 100%);
    --bg-secondary: var(--street-charcoal);
    --bg-card: rgba(26, 26, 26, 0.95);
    --bg-overlay: rgba(255, 255, 255, 0.1);
    
    --text-primary: var(--street-white);
    --text-secondary: var(--street-off-white);
    --text-muted: var(--street-steel);
    
    --border-primary: var(--street-steel);
    --border-accent: var(--street-electric);
}

/* Base Styles */
body {
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-street);
    font-weight: 400;
    line-height: 1.6;
    transition: var(--transition-smooth);
    overflow-x: hidden;
}

/* Remove childish animations */
* {
    animation-duration: 0.3s !important;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Bold Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-street);
    font-weight: 800;
    letter-spacing: -0.02em;
    line-height: 1.2;
    text-transform: uppercase;
}

.hero-title {
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 900;
    letter-spacing: -0.05em;
    text-transform: uppercase;
    background: linear-gradient(45deg, var(--street-red), var(--street-electric));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.section-title {
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: -0.03em;
    margin-bottom: 2rem;
}

/* Aggressive Buttons */
.btn-street {
    background: var(--street-black);
    color: var(--street-white);
    border: 2px solid var(--street-black);
    padding: 1rem 2rem;
    font-family: var(--font-street);
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    cursor: pointer;
    transition: var(--transition-snap);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-block;
}

.btn-street::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--street-red);
    transition: var(--transition-snap);
    z-index: -1;
}

.btn-street:hover::before {
    left: 0;
}

.btn-street:hover {
    color: var(--street-white);
    border-color: var(--street-red);
    transform: translateY(-2px);
    box-shadow: var(--shadow-harsh);
}

.btn-street-outline {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-primary);
}

.btn-street-outline:hover {
    background: var(--text-primary);
    color: var(--bg-secondary);
}

/* Bold Cards */
.street-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 0;
    box-shadow: var(--shadow-deep);
    transition: var(--transition-snap);
    position: relative;
    overflow: hidden;
}

.street-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--street-red), var(--street-electric), var(--street-lime));
    transform: scaleX(0);
    transition: var(--transition-snap);
}

.street-card:hover::after {
    transform: scaleX(1);
}

.street-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.9);
}

/* Product Cards - Aggressive Style */
.product-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 0;
    box-shadow: var(--shadow-deep);
    transition: var(--transition-snap);
    position: relative;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.9);
    border-color: var(--border-accent);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    transition: var(--transition-smooth);
    filter: grayscale(0.2) contrast(1.1);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
    filter: grayscale(0) contrast(1.2);
}

.product-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: var(--transition-snap);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-overlay button {
    background: var(--street-white);
    color: var(--street-black);
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: var(--transition-snap);
}

.product-overlay button:hover {
    background: var(--street-red);
    color: var(--street-white);
    transform: scale(1.05);
}

/* Navigation - Bold & Clean */
.navbar {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    backdrop-filter: blur(20px);
}

.nav-link {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-primary);
    transition: var(--transition-snap);
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--street-red);
    transition: var(--transition-snap);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Hero Section - Bold & Impactful */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    z-index: 2;
    position: relative;
}

.hero-subtitle {
    font-size: clamp(1.25rem, 3vw, 2rem);
    font-weight: 400;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    text-transform: none;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    margin-top: 3rem;
}

/* Remove excessive glow effects */
.neon-text {
    color: var(--street-red);
    text-shadow: none;
    animation: none;
}

/* Simplified theme toggle */
.theme-toggle {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 0;
    box-shadow: var(--shadow-deep);
}

.theme-btn {
    background: transparent;
    border: 1px solid var(--border-primary);
    border-radius: 0;
    transition: var(--transition-snap);
}

.theme-btn:hover,
.theme-btn.active {
    background: var(--street-red);
    color: var(--street-white);
}

/* Remove floating particles for cleaner look */
.cyber-particles {
    display: none;
}

/* Mood indicators - subtle */
[data-mood="energetic"] {
    --accent-color: var(--street-red);
}

[data-mood="calm"] {
    --accent-color: var(--street-electric);
}

[data-mood="mysterious"] {
    --accent-color: var(--street-purple);
}

/* Form elements - bold */
input, select, textarea {
    background: var(--bg-secondary);
    border: 2px solid var(--border-primary);
    border-radius: 0;
    color: var(--text-primary);
    font-family: var(--font-street);
    font-weight: 500;
    padding: 1rem;
    transition: var(--transition-snap);
}

input:focus, select:focus, textarea:focus {
    border-color: var(--street-red);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: clamp(2rem, 10vw, 4rem);
    }
    
    .section-title {
        font-size: clamp(1.5rem, 8vw, 2.5rem);
    }
    
    .btn-street {
        padding: 0.875rem 1.5rem;
        font-size: 0.8rem;
    }
}

/* Remove childish hover effects */
.floating-product {
    animation: none;
}

.ai-graffiti {
    display: none;
}

/* Simplify AI assistant */
.ai-style-assistant .style-toggle {
    background: var(--street-black);
    border: 2px solid var(--street-red);
    border-radius: 0;
    box-shadow: var(--shadow-deep);
}

.ai-style-assistant .style-toggle:hover {
    background: var(--street-red);
    transform: none;
    box-shadow: var(--shadow-deep);
}

/* Bold footer */
.footer {
    background: var(--street-black);
    color: var(--street-white);
    border-top: 4px solid var(--street-red);
}

.footer-title {
    color: var(--street-white);
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.footer-links a {
    color: var(--street-off-white);
    font-weight: 500;
    transition: var(--transition-snap);
}

.footer-links a:hover {
    color: var(--street-red);
}
