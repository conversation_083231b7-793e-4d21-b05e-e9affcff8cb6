/* Bold Aggressive Streetwear Theme */

:root {
    /* Bold Streetwear Color Palette */
    --street-black: #0a0a0a;
    --street-charcoal: #1a1a1a;
    --street-concrete: #2a2a2a;
    --street-steel: #3a3a3a;
    --street-white: #f8f8f8;
    --street-off-white: #e8e8e8;
    
    /* Aggressive Accent Colors */
    --street-red: #ff0000;
    --street-orange: #ff4500;
    --street-electric: #00ffff;
    --street-lime: #32ff32;
    --street-purple: #8a2be2;
    --street-gold: #ffd700;
    
    /* Typography */
    --font-street: 'Inter', 'Helvetica Neue', Arial, sans-serif;
    --font-display: 'Playfair Display', Georgia, serif;
    --font-mono: 'JetBrains Mono', 'Courier New', monospace;
    
    /* Shadows & Effects */
    --shadow-harsh: 0 4px 0 var(--street-charcoal);
    --shadow-deep: 0 8px 24px rgba(0, 0, 0, 0.8);
    --shadow-glow: 0 0 20px;
    
    /* Transitions */
    --transition-snap: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Light Theme - Urban Day */
[data-theme="light"] {
    --bg-primary: linear-gradient(135deg, var(--street-white) 0%, var(--street-off-white) 100%);
    --bg-secondary: var(--street-white);
    --bg-card: rgba(255, 255, 255, 0.95);
    --bg-overlay: rgba(0, 0, 0, 0.1);
    
    --text-primary: var(--street-black);
    --text-secondary: var(--street-charcoal);
    --text-muted: var(--street-concrete);
    
    --border-primary: var(--street-concrete);
    --border-accent: var(--street-red);
}

/* Dark Theme - Urban Night */
[data-theme="dark"] {
    --bg-primary: linear-gradient(135deg, var(--street-black) 0%, var(--street-charcoal) 100%);
    --bg-secondary: var(--street-charcoal);
    --bg-card: rgba(26, 26, 26, 0.95);
    --bg-overlay: rgba(255, 255, 255, 0.1);
    
    --text-primary: var(--street-white);
    --text-secondary: var(--street-off-white);
    --text-muted: var(--street-steel);
    
    --border-primary: var(--street-steel);
    --border-accent: var(--street-electric);
}

/* Base Styles */
body {
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-street);
    font-weight: 400;
    line-height: 1.6;
    transition: var(--transition-smooth);
    overflow-x: hidden;
}

/* Remove childish animations */
* {
    animation-duration: 0.3s !important;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Bold Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-street);
    font-weight: 800;
    letter-spacing: -0.02em;
    line-height: 1.2;
    text-transform: uppercase;
}

.hero-title {
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 900;
    letter-spacing: -0.05em;
    text-transform: uppercase;
    background: linear-gradient(45deg, var(--street-red), var(--street-electric));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.section-title {
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: -0.03em;
    margin-bottom: 2rem;
}

/* Aggressive Buttons */
.btn-street {
    background: var(--street-black);
    color: var(--street-white);
    border: 2px solid var(--street-black);
    padding: 1rem 2rem;
    font-family: var(--font-street);
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    cursor: pointer;
    transition: var(--transition-snap);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-block;
}

.btn-street::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--street-red);
    transition: var(--transition-snap);
    z-index: -1;
}

.btn-street:hover::before {
    left: 0;
}

.btn-street:hover {
    color: var(--street-white);
    border-color: var(--street-red);
    transform: translateY(-2px);
    box-shadow: var(--shadow-harsh);
}

.btn-street-outline {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-primary);
}

.btn-street-outline:hover {
    background: var(--text-primary);
    color: var(--bg-secondary);
}

/* Bold Cards */
.street-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 0;
    box-shadow: var(--shadow-deep);
    transition: var(--transition-snap);
    position: relative;
    overflow: hidden;
}

.street-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--street-red), var(--street-electric), var(--street-lime));
    transform: scaleX(0);
    transition: var(--transition-snap);
}

.street-card:hover::after {
    transform: scaleX(1);
}

.street-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.9);
}

/* Product Cards - Aggressive Style */
.product-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 0;
    box-shadow: var(--shadow-deep);
    transition: var(--transition-snap);
    position: relative;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.9);
    border-color: var(--border-accent);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    transition: var(--transition-smooth);
    filter: grayscale(0.2) contrast(1.1);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
    filter: grayscale(0) contrast(1.2);
}

.product-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: var(--transition-snap);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-overlay button {
    background: var(--street-white);
    color: var(--street-black);
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: var(--transition-snap);
}

.product-overlay button:hover {
    background: var(--street-red);
    color: var(--street-white);
    transform: scale(1.05);
}

/* Navigation - Bold & Clean */
.navbar {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    backdrop-filter: blur(20px);
}

.nav-link {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-primary);
    transition: var(--transition-snap);
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--street-red);
    transition: var(--transition-snap);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Hero Section - Bold & Impactful */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    z-index: 2;
    position: relative;
}

.hero-subtitle {
    font-size: clamp(1.25rem, 3vw, 2rem);
    font-weight: 400;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    text-transform: none;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    margin-top: 3rem;
}

/* Remove excessive glow effects */
.neon-text {
    color: var(--street-red);
    text-shadow: none;
    animation: none;
}

/* Mature Theme Controls */
.theme-toggle {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 1001;
    background: var(--street-black);
    border: 2px solid var(--street-red);
    border-radius: 0;
    padding: 0;
    box-shadow: var(--shadow-deep);
    overflow: hidden;
}

.theme-controls {
    display: flex;
    flex-direction: column;
}

.theme-section {
    border-bottom: 1px solid var(--street-concrete);
    padding: 0.75rem;
}

.theme-section:last-child {
    border-bottom: none;
}

.theme-section-title {
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--street-red);
    margin-bottom: 0.5rem;
}

.theme-btn {
    width: 40px;
    height: 40px;
    background: var(--street-charcoal);
    border: 1px solid var(--street-concrete);
    border-radius: 0;
    color: var(--street-white);
    cursor: pointer;
    transition: var(--transition-snap);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    margin: 0.25rem 0;
    position: relative;
    overflow: hidden;
}

.theme-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--street-red);
    transition: var(--transition-snap);
    z-index: -1;
}

.theme-btn:hover::before,
.theme-btn.active::before {
    left: 0;
}

.theme-btn:hover,
.theme-btn.active {
    color: var(--street-white);
    border-color: var(--street-red);
}

.mood-controls {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.mood-btn {
    width: 40px;
    height: 30px;
    border: 1px solid var(--street-concrete);
    border-radius: 0;
    cursor: pointer;
    transition: var(--transition-snap);
    position: relative;
    overflow: hidden;
}

.mood-energetic {
    background: linear-gradient(45deg, var(--street-red), var(--street-orange));
}

.mood-calm {
    background: linear-gradient(45deg, var(--street-electric), #4ecdc4);
}

.mood-mysterious {
    background: linear-gradient(45deg, var(--street-purple), #8000ff);
}

.mood-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.3);
    transition: var(--transition-snap);
}

.mood-btn:hover::before,
.mood-btn.active::before {
    background: transparent;
}

.mood-btn:hover,
.mood-btn.active {
    transform: scale(1.05);
    border-color: var(--street-white);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Remove floating particles for cleaner look */
.cyber-particles {
    display: none;
}

/* Mood indicators - subtle */
[data-mood="energetic"] {
    --accent-color: var(--street-red);
}

[data-mood="calm"] {
    --accent-color: var(--street-electric);
}

[data-mood="mysterious"] {
    --accent-color: var(--street-purple);
}

/* Form elements - bold */
input, select, textarea {
    background: var(--bg-secondary);
    border: 2px solid var(--border-primary);
    border-radius: 0;
    color: var(--text-primary);
    font-family: var(--font-street);
    font-weight: 500;
    padding: 1rem;
    transition: var(--transition-snap);
}

input:focus, select:focus, textarea:focus {
    border-color: var(--street-red);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: clamp(2rem, 10vw, 4rem);
    }
    
    .section-title {
        font-size: clamp(1.5rem, 8vw, 2.5rem);
    }
    
    .btn-street {
        padding: 0.875rem 1.5rem;
        font-size: 0.8rem;
    }
}

/* Remove childish hover effects */
.floating-product {
    animation: none;
}

.ai-graffiti {
    display: none;
}

/* Simplify AI assistant */
.ai-style-assistant .style-toggle {
    background: var(--street-black);
    border: 2px solid var(--street-red);
    border-radius: 0;
    box-shadow: var(--shadow-deep);
}

.ai-style-assistant .style-toggle:hover {
    background: var(--street-red);
    transform: none;
    box-shadow: var(--shadow-deep);
}

/* AI Avatar Try-On System */
.ai-avatar-system {
    position: fixed;
    bottom: 180px;
    right: 20px;
    z-index: 998;
}

.avatar-toggle {
    background: var(--street-black);
    border: 2px solid var(--street-electric);
    color: var(--street-white);
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition-snap);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
}

.avatar-toggle:hover {
    background: var(--street-electric);
    color: var(--street-black);
    transform: translateY(-2px);
}

.avatar-icon {
    font-size: 1.25rem;
}

.avatar-panel {
    position: absolute;
    bottom: 60px;
    right: 0;
    width: 400px;
    max-height: 600px;
    background: var(--street-charcoal);
    border: 2px solid var(--street-electric);
    box-shadow: var(--shadow-deep);
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.avatar-panel.active {
    display: flex;
}

.avatar-header {
    background: var(--street-black);
    color: var(--street-white);
    padding: 1rem;
    border-bottom: 1px solid var(--street-concrete);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.avatar-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.avatar-header p {
    margin: 0;
    font-size: 0.75rem;
    color: var(--street-electric);
    text-transform: uppercase;
}

.close-avatar-btn {
    background: transparent;
    border: 1px solid var(--street-concrete);
    color: var(--street-white);
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-snap);
}

.close-avatar-btn:hover {
    background: var(--street-red);
    border-color: var(--street-red);
}

.avatar-content {
    padding: 1rem;
    overflow-y: auto;
    flex: 1;
}

.avatar-selection h4,
.size-recommendations h4 {
    color: var(--street-white);
    font-size: 0.875rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.75rem;
    border-bottom: 1px solid var(--street-red);
    padding-bottom: 0.25rem;
}

.avatar-models {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.avatar-model-btn {
    background: var(--street-black);
    border: 1px solid var(--street-concrete);
    color: var(--street-white);
    padding: 0.75rem;
    cursor: pointer;
    transition: var(--transition-snap);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-align: left;
}

.avatar-model-btn:hover,
.avatar-model-btn.active {
    border-color: var(--street-electric);
    background: var(--street-charcoal);
}

.model-preview {
    width: 40px;
    height: 40px;
    background: var(--street-concrete);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--street-electric);
}

.model-info {
    display: flex;
    flex-direction: column;
}

.model-name {
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.model-details {
    font-size: 0.75rem;
    color: var(--street-off-white);
    text-transform: uppercase;
}

.avatar-viewer {
    background: var(--street-black);
    border: 1px solid var(--street-concrete);
    margin-bottom: 1.5rem;
    position: relative;
}

.avatar-canvas {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.avatar-model {
    width: 80px;
    height: 160px;
    position: relative;
    transition: transform 0.5s ease;
}

.model-base {
    width: 100%;
    height: 100%;
    position: relative;
}

.model-head {
    width: 20px;
    height: 20px;
    background: var(--street-off-white);
    border-radius: 50%;
    margin: 0 auto 5px;
}

.model-torso {
    width: 30px;
    height: 60px;
    background: var(--street-concrete);
    margin: 0 auto 5px;
}

.model-arms {
    position: absolute;
    top: 25px;
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.model-arm {
    width: 8px;
    height: 40px;
    background: var(--street-concrete);
}

.model-legs {
    display: flex;
    justify-content: center;
    gap: 2px;
}

.model-leg {
    width: 12px;
    height: 70px;
    background: var(--street-concrete);
}

.product-overlay {
    position: absolute;
    inset: 0;
    pointer-events: none;
}

.avatar-controls {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--street-charcoal);
    border-top: 1px solid var(--street-concrete);
}

.avatar-control-btn {
    background: var(--street-black);
    border: 1px solid var(--street-concrete);
    color: var(--street-white);
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-snap);
}

.avatar-control-btn:hover {
    background: var(--street-electric);
    color: var(--street-black);
    border-color: var(--street-electric);
}

.tryon-product-info {
    margin-bottom: 1.5rem;
}

.no-product-selected {
    text-align: center;
    color: var(--street-off-white);
    padding: 2rem;
}

.no-product-selected i {
    font-size: 2rem;
    color: var(--street-concrete);
    margin-bottom: 0.5rem;
}

.selected-product {
    display: flex;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--street-black);
    border: 1px solid var(--street-concrete);
}

.selected-product .product-image {
    width: 60px;
    height: 60px;
    overflow: hidden;
}

.selected-product .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.selected-product .product-details h5 {
    color: var(--street-white);
    font-size: 0.875rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
}

.selected-product .product-details p {
    color: var(--street-off-white);
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
}

.selected-product .price {
    color: var(--street-electric);
    font-weight: 700;
    font-size: 0.875rem;
}

.size-analysis {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.recommended-size,
.fit-analysis {
    flex: 1;
    text-align: center;
}

.size-label,
.fit-label {
    display: block;
    font-size: 0.75rem;
    color: var(--street-off-white);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
}

.size-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--street-electric);
    text-transform: uppercase;
}

.fit-description {
    display: block;
    font-size: 0.75rem;
    color: var(--street-white);
    text-transform: uppercase;
}

.tryon-actions {
    display: flex;
    gap: 0.5rem;
}

.tryon-btn {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid;
    cursor: pointer;
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: var(--transition-snap);
}

.tryon-btn.primary {
    background: var(--street-red);
    border-color: var(--street-red);
    color: var(--street-white);
}

.tryon-btn.primary:hover {
    background: transparent;
    color: var(--street-red);
}

.tryon-btn.secondary {
    background: transparent;
    border-color: var(--street-electric);
    color: var(--street-electric);
}

.tryon-btn.secondary:hover {
    background: var(--street-electric);
    color: var(--street-black);
}

/* Product Visualizations */
.hoodie-overlay {
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
    width: 35px;
    height: 65px;
}

.hoodie-body {
    width: 100%;
    height: 50px;
    background: var(--street-red);
    opacity: 0.8;
}

.hoodie-hood {
    width: 25px;
    height: 15px;
    background: var(--street-red);
    opacity: 0.6;
    margin: -5px auto 0;
    border-radius: 50% 50% 0 0;
}

.tshirt-overlay {
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 45px;
}

.tshirt-body {
    width: 100%;
    height: 100%;
    background: var(--street-electric);
    opacity: 0.8;
}

.jewelry-overlay {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
}

.necklace {
    width: 20px;
    height: 3px;
    background: var(--street-gold);
    border-radius: 2px;
}

/* Responsive */
@media (max-width: 480px) {
    .avatar-panel {
        width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
    }

    .size-analysis {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Bold footer */
.footer {
    background: var(--street-black);
    color: var(--street-white);
    border-top: 4px solid var(--street-red);
}

.footer-title {
    color: var(--street-white);
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.footer-links a {
    color: var(--street-off-white);
    font-weight: 500;
    transition: var(--transition-snap);
}

.footer-links a:hover {
    color: var(--street-red);
}

/* Pinterest Integration */
.pinterest-save-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 35px;
    height: 35px;
    background: #e60023;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: var(--transition-snap);
    opacity: 0;
    transform: scale(0.8);
}

.product-card:hover .pinterest-save-btn {
    opacity: 1;
    transform: scale(1);
}

.pinterest-save-btn:hover {
    background: #ad081b;
    transform: scale(1.1);
}

.pinterest-board-link {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
    background: var(--street-black);
    border: 2px solid #e60023;
    color: #e60023;
    padding: 0.75rem 1rem;
    text-decoration: none;
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: var(--transition-snap);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pinterest-board-link:hover {
    background: #e60023;
    color: white;
    transform: translateY(-2px);
}

.pinterest-inspiration {
    padding: 4rem 0;
    background: var(--street-charcoal);
}

.pinterest-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.pinterest-pin {
    position: relative;
    overflow: hidden;
    border-radius: 0;
    background: var(--street-black);
    border: 1px solid var(--street-concrete);
}

.pinterest-pin img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.pin-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-snap);
}

.pinterest-pin:hover .pin-overlay {
    opacity: 1;
}

.pinterest-pin:hover img {
    transform: scale(1.05);
}

.pin-link {
    background: #e60023;
    color: white;
    padding: 0.75rem 1rem;
    text-decoration: none;
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-snap);
}

.pin-link:hover {
    background: #ad081b;
    transform: scale(1.05);
}

.pinterest-cta {
    text-align: center;
}

.pinterest-success-message {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: #e60023;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    z-index: 1002;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Pinterest */
@media (max-width: 768px) {
    .pinterest-board-link {
        bottom: 1rem;
        right: 1rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .pinterest-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .pinterest-pin img {
        height: 250px;
    }
}
