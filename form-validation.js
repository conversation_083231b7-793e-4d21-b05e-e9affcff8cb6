// Advanced Form Validation System
class FormValidator {
    constructor() {
        this.validators = {
            email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            phone: /^[\+]?[1-9][\d]{0,15}$/,
            name: /^[a-zA-ZÀ-ÿ\s'-]{2,50}$/,
            password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
            creditCard: /^[0-9]{13,19}$/,
            cvv: /^[0-9]{3,4}$/,
            zipCode: /^[0-9]{5}(-[0-9]{4})?$/,
            address: /^[a-zA-Z0-9\s,.-]{5,100}$/,
            city: /^[a-zA-ZÀ-ÿ\s'-]{2,50}$/,
            orderNumber: /^M5-\d{4}-\d{6}$/,
            promoCode: /^[A-Z0-9]{4,20}$/
        };
        
        this.countries = this.getCountriesList();
        this.init();
    }

    init() {
        this.setupRealTimeValidation();
        this.setupFormSubmissionHandling();
        this.addValidationStyles();
    }

    setupRealTimeValidation() {
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, select, textarea')) {
                this.validateField(e.target);
            }
        });

        document.addEventListener('blur', (e) => {
            if (e.target.matches('input, select, textarea')) {
                this.validateField(e.target, true);
            }
        });

        document.addEventListener('focus', (e) => {
            if (e.target.matches('input, select, textarea')) {
                this.clearFieldError(e.target);
            }
        });
    }

    validateField(field, showError = false) {
        const value = field.value.trim();
        const fieldType = field.dataset.validate || field.type || field.name;
        const isRequired = field.hasAttribute('required');
        
        // Clear previous validation state
        this.clearFieldValidation(field);
        
        // Check if field is empty and required
        if (isRequired && !value) {
            if (showError) {
                this.showFieldError(field, 'This field is required');
            }
            return false;
        }
        
        // Skip validation if field is empty and not required
        if (!value && !isRequired) {
            return true;
        }
        
        // Validate based on field type
        let isValid = true;
        let errorMessage = '';
        
        switch (fieldType) {
            case 'email':
                isValid = this.validators.email.test(value);
                errorMessage = 'Please enter a valid email address';
                break;
                
            case 'phone':
            case 'tel':
                isValid = this.validators.phone.test(value.replace(/[\s()-]/g, ''));
                errorMessage = 'Please enter a valid phone number';
                break;
                
            case 'name':
            case 'firstName':
            case 'lastName':
                isValid = this.validators.name.test(value);
                errorMessage = 'Name should only contain letters, spaces, hyphens, and apostrophes';
                break;
                
            case 'password':
                isValid = this.validators.password.test(value);
                errorMessage = 'Password must be at least 8 characters with uppercase, lowercase, number, and special character';
                break;
                
            case 'confirmPassword':
                const passwordField = document.querySelector('input[name="password"], input[type="password"]');
                isValid = passwordField && value === passwordField.value;
                errorMessage = 'Passwords do not match';
                break;
                
            case 'creditCard':
                isValid = this.validateCreditCard(value);
                errorMessage = 'Please enter a valid credit card number';
                break;
                
            case 'cvv':
                isValid = this.validators.cvv.test(value);
                errorMessage = 'Please enter a valid CVV (3-4 digits)';
                break;
                
            case 'zipCode':
            case 'postalCode':
                isValid = this.validatePostalCode(value, field);
                errorMessage = 'Please enter a valid postal/ZIP code';
                break;
                
            case 'address':
                isValid = this.validators.address.test(value);
                errorMessage = 'Please enter a valid address';
                break;
                
            case 'city':
                isValid = this.validators.city.test(value);
                errorMessage = 'Please enter a valid city name';
                break;
                
            case 'country':
                isValid = this.countries.some(country => country.code === value || country.name === value);
                errorMessage = 'Please select a valid country';
                break;
                
            case 'orderNumber':
                isValid = this.validators.orderNumber.test(value);
                errorMessage = 'Order number format: M5-YYYY-XXXXXX';
                break;
                
            case 'promoCode':
                isValid = this.validators.promoCode.test(value);
                errorMessage = 'Promo code should be 4-20 alphanumeric characters';
                break;
                
            case 'age':
                const age = parseInt(value);
                isValid = age >= 13 && age <= 120;
                errorMessage = 'Age must be between 13 and 120';
                break;
                
            case 'number':
                isValid = !isNaN(value) && isFinite(value);
                errorMessage = 'Please enter a valid number';
                break;
                
            default:
                // Custom validation based on data attributes
                if (field.dataset.minLength) {
                    const minLength = parseInt(field.dataset.minLength);
                    if (value.length < minLength) {
                        isValid = false;
                        errorMessage = `Minimum ${minLength} characters required`;
                    }
                }
                
                if (field.dataset.maxLength) {
                    const maxLength = parseInt(field.dataset.maxLength);
                    if (value.length > maxLength) {
                        isValid = false;
                        errorMessage = `Maximum ${maxLength} characters allowed`;
                    }
                }
                
                if (field.dataset.pattern) {
                    const pattern = new RegExp(field.dataset.pattern);
                    if (!pattern.test(value)) {
                        isValid = false;
                        errorMessage = field.dataset.patternMessage || 'Invalid format';
                    }
                }
        }
        
        // Show validation result
        if (isValid) {
            this.showFieldSuccess(field);
        } else if (showError) {
            this.showFieldError(field, errorMessage);
        }
        
        return isValid;
    }

    validateCreditCard(cardNumber) {
        // Remove spaces and hyphens
        const cleanNumber = cardNumber.replace(/[\s-]/g, '');
        
        // Check if it's a valid number
        if (!this.validators.creditCard.test(cleanNumber)) {
            return false;
        }
        
        // Luhn algorithm
        let sum = 0;
        let isEven = false;
        
        for (let i = cleanNumber.length - 1; i >= 0; i--) {
            let digit = parseInt(cleanNumber.charAt(i));
            
            if (isEven) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }
            
            sum += digit;
            isEven = !isEven;
        }
        
        return sum % 10 === 0;
    }

    validatePostalCode(value, field) {
        const countryField = document.querySelector('select[name="country"]');
        const country = countryField ? countryField.value : 'US';
        
        const postalPatterns = {
            'US': /^[0-9]{5}(-[0-9]{4})?$/,
            'CA': /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/,
            'GB': /^[A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}$/i,
            'DE': /^[0-9]{5}$/,
            'FR': /^[0-9]{5}$/,
            'AU': /^[0-9]{4}$/,
            'JP': /^[0-9]{3}-[0-9]{4}$/,
            'KE': /^[0-9]{5}$/,
            'NG': /^[0-9]{6}$/,
            'ZA': /^[0-9]{4}$/
        };
        
        const pattern = postalPatterns[country] || /^[A-Za-z0-9\s-]{3,10}$/;
        return pattern.test(value);
    }

    showFieldError(field, message) {
        field.classList.add('error');
        field.classList.remove('success');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
        field.parentNode.appendChild(errorDiv);
        
        // Add shake animation
        field.classList.add('shake');
        setTimeout(() => field.classList.remove('shake'), 500);
    }

    showFieldSuccess(field) {
        field.classList.add('success');
        field.classList.remove('error');
        
        // Remove error message
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // Add success icon
        let successIcon = field.parentNode.querySelector('.success-icon');
        if (!successIcon) {
            successIcon = document.createElement('div');
            successIcon.className = 'success-icon';
            successIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            field.parentNode.appendChild(successIcon);
        }
    }

    clearFieldValidation(field) {
        field.classList.remove('error', 'success');
        const errorMessage = field.parentNode.querySelector('.error-message');
        const successIcon = field.parentNode.querySelector('.success-icon');
        
        if (errorMessage) errorMessage.remove();
        if (successIcon) successIcon.remove();
    }

    clearFieldError(field) {
        field.classList.remove('error');
        const errorMessage = field.parentNode.querySelector('.error-message');
        if (errorMessage) {
            errorMessage.style.opacity = '0.5';
        }
    }

    setupFormSubmissionHandling() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.tagName === 'FORM') {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    this.showFormError(form, 'Please correct the errors above');
                }
            }
        });
    }

    validateForm(form) {
        const fields = form.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        fields.forEach(field => {
            if (!this.validateField(field, true)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    showFormError(form, message) {
        let errorDiv = form.querySelector('.form-error');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'form-error';
            form.insertBefore(errorDiv, form.firstChild);
        }
        
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            ${message}
        `;
        
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    addValidationStyles() {
        const styles = `
            .form-group {
                position: relative;
                margin-bottom: 1.5rem;
            }
            
            input.error,
            select.error,
            textarea.error {
                border-color: #ef4444 !important;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
                animation: shake 0.5s ease-in-out;
            }
            
            input.success,
            select.success,
            textarea.success {
                border-color: #10b981 !important;
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
            }
            
            .error-message {
                color: #ef4444;
                font-size: 0.875rem;
                margin-top: 0.5rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                animation: slideDown 0.3s ease-out;
            }
            
            .success-icon {
                position: absolute;
                right: 0.75rem;
                top: 50%;
                transform: translateY(-50%);
                color: #10b981;
                pointer-events: none;
            }
            
            .form-error {
                background: rgba(239, 68, 68, 0.1);
                border: 1px solid #ef4444;
                color: #ef4444;
                padding: 1rem;
                border-radius: 0.5rem;
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                animation: slideDown 0.3s ease-out;
            }
            
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
            
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            /* Real-time validation indicators */
            .form-group input:focus {
                outline: none;
                border-color: var(--neon-primary);
                box-shadow: 0 0 0 3px rgba(0, 245, 255, 0.1);
            }
            
            .form-group input:valid:not(:placeholder-shown) {
                border-color: #10b981;
            }
            
            .form-group input:invalid:not(:placeholder-shown):not(:focus) {
                border-color: #ef4444;
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    getCountriesList() {
        return [
            { code: 'US', name: 'United States' },
            { code: 'CA', name: 'Canada' },
            { code: 'GB', name: 'United Kingdom' },
            { code: 'AU', name: 'Australia' },
            { code: 'DE', name: 'Germany' },
            { code: 'FR', name: 'France' },
            { code: 'IT', name: 'Italy' },
            { code: 'ES', name: 'Spain' },
            { code: 'NL', name: 'Netherlands' },
            { code: 'BE', name: 'Belgium' },
            { code: 'CH', name: 'Switzerland' },
            { code: 'AT', name: 'Austria' },
            { code: 'SE', name: 'Sweden' },
            { code: 'NO', name: 'Norway' },
            { code: 'DK', name: 'Denmark' },
            { code: 'FI', name: 'Finland' },
            { code: 'JP', name: 'Japan' },
            { code: 'KR', name: 'South Korea' },
            { code: 'CN', name: 'China' },
            { code: 'SG', name: 'Singapore' },
            { code: 'HK', name: 'Hong Kong' },
            { code: 'IN', name: 'India' },
            { code: 'AE', name: 'United Arab Emirates' },
            { code: 'SA', name: 'Saudi Arabia' },
            { code: 'IL', name: 'Israel' },
            { code: 'ZA', name: 'South Africa' },
            { code: 'KE', name: 'Kenya' },
            { code: 'NG', name: 'Nigeria' },
            { code: 'GH', name: 'Ghana' },
            { code: 'EG', name: 'Egypt' },
            { code: 'MA', name: 'Morocco' },
            { code: 'TN', name: 'Tunisia' },
            { code: 'BR', name: 'Brazil' },
            { code: 'MX', name: 'Mexico' },
            { code: 'AR', name: 'Argentina' },
            { code: 'CL', name: 'Chile' },
            { code: 'CO', name: 'Colombia' },
            { code: 'PE', name: 'Peru' },
            { code: 'RU', name: 'Russia' },
            { code: 'PL', name: 'Poland' },
            { code: 'CZ', name: 'Czech Republic' },
            { code: 'HU', name: 'Hungary' },
            { code: 'RO', name: 'Romania' },
            { code: 'BG', name: 'Bulgaria' },
            { code: 'HR', name: 'Croatia' },
            { code: 'SI', name: 'Slovenia' },
            { code: 'SK', name: 'Slovakia' },
            { code: 'LT', name: 'Lithuania' },
            { code: 'LV', name: 'Latvia' },
            { code: 'EE', name: 'Estonia' },
            { code: 'IE', name: 'Ireland' },
            { code: 'PT', name: 'Portugal' },
            { code: 'GR', name: 'Greece' },
            { code: 'TR', name: 'Turkey' },
            { code: 'TH', name: 'Thailand' },
            { code: 'VN', name: 'Vietnam' },
            { code: 'MY', name: 'Malaysia' },
            { code: 'ID', name: 'Indonesia' },
            { code: 'PH', name: 'Philippines' },
            { code: 'NZ', name: 'New Zealand' }
        ];
    }

    // Public API
    validateSingleField(fieldSelector) {
        const field = document.querySelector(fieldSelector);
        if (field) {
            return this.validateField(field, true);
        }
        return false;
    }

    getCountries() {
        return this.countries;
    }
}

// Initialize form validator
document.addEventListener('DOMContentLoaded', () => {
    window.formValidator = new FormValidator();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FormValidator;
}
