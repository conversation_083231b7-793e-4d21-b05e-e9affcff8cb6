// Advanced Checkout System with Mobile Money Integration
class CheckoutSystem {
    constructor() {
        this.currentStep = 1;
        this.maxSteps = 3;
        this.orderData = {};
        this.cartItems = JSON.parse(localStorage.getItem('magna5rrr_cart') || '[]');
        this.paymentProcessors = this.initializePaymentProcessors();
        this.init();
    }

    initializePaymentProcessors() {
        return {
            card: {
                name: 'Stripe',
                apiKey: 'pk_test_magna5rrr_stripe_key',
                endpoint: '/api/payments/card'
            },
            mobile: {
                mpesa: {
                    name: 'M-Pesa',
                    shortcode: '174379',
                    endpoint: '/api/payments/mpesa'
                },
                mtn: {
                    name: 'MTN Mobile Money',
                    endpoint: '/api/payments/mtn'
                },
                airtel: {
                    name: 'Airtel Money',
                    endpoint: '/api/payments/airtel'
                }
            },
            paypal: {
                name: 'PayPal',
                clientId: 'magna5rrr_paypal_client_id',
                endpoint: '/api/payments/paypal'
            }
        };
    }

    init() {
        this.loadCartItems();
        this.setupEventListeners();
        this.updateProgress();
        this.calculateTotals();
        this.setupFormValidation();
    }

    setupEventListeners() {
        // Step navigation
        document.querySelectorAll('.next-step').forEach(btn => {
            btn.addEventListener('click', () => this.nextStep());
        });

        document.querySelectorAll('.prev-step').forEach(btn => {
            btn.addEventListener('click', () => this.prevStep());
        });

        // Payment method selection
        document.querySelectorAll('input[name="paymentMethod"]').forEach(radio => {
            radio.addEventListener('change', (e) => this.selectPaymentMethod(e.target.value));
        });

        // Shipping method selection
        document.querySelectorAll('input[name="shipping"]').forEach(radio => {
            radio.addEventListener('change', () => this.calculateTotals());
        });

        // Form submission
        document.getElementById('checkoutForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processOrder();
        });

        // Card number formatting
        const cardNumberInput = document.getElementById('cardNumber');
        if (cardNumberInput) {
            cardNumberInput.addEventListener('input', this.formatCardNumber);
        }

        // Expiry date formatting
        const expiryInput = document.getElementById('expiryDate');
        if (expiryInput) {
            expiryInput.addEventListener('input', this.formatExpiryDate);
        }

        // CVV validation
        const cvvInput = document.getElementById('cvv');
        if (cvvInput) {
            cvvInput.addEventListener('input', this.formatCVV);
        }

        // Mobile number formatting
        const mobileInput = document.getElementById('mobileNumber');
        if (mobileInput) {
            mobileInput.addEventListener('input', this.formatMobileNumber);
        }

        // Country change handler
        const countrySelect = document.getElementById('country');
        if (countrySelect) {
            countrySelect.addEventListener('change', (e) => this.handleCountryChange(e.target.value));
        }

        // Promo code
        document.getElementById('applyPromo').addEventListener('click', () => this.applyPromoCode());

        // Edit buttons
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const step = parseInt(e.target.dataset.step);
                this.goToStep(step);
            });
        });
    }

    loadCartItems() {
        const orderItemsContainer = document.getElementById('orderItems');
        if (!orderItemsContainer || this.cartItems.length === 0) {
            orderItemsContainer.innerHTML = '<p>No items in cart</p>';
            return;
        }

        orderItemsContainer.innerHTML = this.cartItems.map(item => `
            <div class="order-item">
                <div class="item-image">
                    <img src="${item.image}" alt="${item.name}" loading="lazy">
                </div>
                <div class="item-details">
                    <h4>${item.name}</h4>
                    <p>${item.category}${item.size ? ` • Size: ${item.size}` : ''}${item.color ? ` • Color: ${item.color}` : ''}</p>
                    <p>Qty: ${item.quantity}</p>
                </div>
                <div class="item-price">$${(item.price * item.quantity).toFixed(2)}</div>
            </div>
        `).join('');
    }

    calculateTotals() {
        const subtotal = this.cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        // Get shipping cost
        const selectedShipping = document.querySelector('input[name="shipping"]:checked');
        let shippingCost = 0;
        let shippingText = 'Free';
        
        if (selectedShipping) {
            switch (selectedShipping.value) {
                case 'express':
                    shippingCost = 9.99;
                    shippingText = '$9.99';
                    break;
                case 'overnight':
                    shippingCost = 24.99;
                    shippingText = '$24.99';
                    break;
            }
        }

        const tax = (subtotal + shippingCost) * 0.08; // 8% tax
        const total = subtotal + shippingCost + tax;

        // Update display
        document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
        document.getElementById('shippingCost').textContent = shippingText;
        document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
        document.getElementById('total').textContent = `$${total.toFixed(2)}`;

        // Store for order processing
        this.orderData.pricing = {
            subtotal,
            shipping: shippingCost,
            tax,
            total
        };
    }

    selectPaymentMethod(method) {
        // Hide all payment method contents
        document.querySelectorAll('.payment-method').forEach(pm => {
            pm.classList.remove('active');
        });

        // Show selected payment method
        const selectedMethod = document.querySelector(`[data-method="${method}"]`);
        if (selectedMethod) {
            selectedMethod.classList.add('active');
        }
    }

    nextStep() {
        if (this.validateCurrentStep()) {
            if (this.currentStep < this.maxSteps) {
                this.currentStep++;
                this.updateStep();
                this.updateProgress();
                
                if (this.currentStep === 3) {
                    this.populateReview();
                }
            }
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStep();
            this.updateProgress();
        }
    }

    goToStep(step) {
        this.currentStep = step;
        this.updateStep();
        this.updateProgress();
    }

    updateStep() {
        // Hide all steps
        document.querySelectorAll('.form-step').forEach(step => {
            step.classList.remove('active');
        });

        // Show current step
        document.getElementById(`step${this.currentStep}`).classList.add('active');
    }

    updateProgress() {
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            if (index < this.currentStep) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
    }

    validateCurrentStep() {
        const currentStepElement = document.getElementById(`step${this.currentStep}`);
        const requiredFields = currentStepElement.querySelectorAll('input[required], select[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'This field is required');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        // Additional validation for specific steps
        if (this.currentStep === 2) {
            isValid = this.validatePaymentMethod() && isValid;
        }

        return isValid;
    }

    validatePaymentMethod() {
        const selectedMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
        
        switch (selectedMethod) {
            case 'card':
                return this.validateCardDetails();
            case 'mobile':
                return this.validateMobileDetails();
            default:
                return true;
        }
    }

    validateCardDetails() {
        const cardNumber = document.getElementById('cardNumber').value.replace(/\s/g, '');
        const expiryDate = document.getElementById('expiryDate').value;
        const cvv = document.getElementById('cvv').value;
        const cardName = document.getElementById('cardName').value;

        let isValid = true;

        // Card number validation (basic Luhn algorithm)
        if (!this.isValidCardNumber(cardNumber)) {
            this.showFieldError(document.getElementById('cardNumber'), 'Invalid card number');
            isValid = false;
        }

        // Expiry date validation
        if (!this.isValidExpiryDate(expiryDate)) {
            this.showFieldError(document.getElementById('expiryDate'), 'Invalid expiry date');
            isValid = false;
        }

        // CVV validation
        if (cvv.length < 3 || cvv.length > 4) {
            this.showFieldError(document.getElementById('cvv'), 'Invalid CVV');
            isValid = false;
        }

        // Card name validation
        if (!cardName.trim()) {
            this.showFieldError(document.getElementById('cardName'), 'Name on card is required');
            isValid = false;
        }

        return isValid;
    }

    validateMobileDetails() {
        const provider = document.getElementById('mobileProvider').value;
        const mobileNumber = document.getElementById('mobileNumber').value;

        let isValid = true;

        if (!provider) {
            this.showFieldError(document.getElementById('mobileProvider'), 'Please select a provider');
            isValid = false;
        }

        if (!this.isValidMobileNumber(mobileNumber, provider)) {
            this.showFieldError(document.getElementById('mobileNumber'), 'Invalid mobile number for selected provider');
            isValid = false;
        }

        return isValid;
    }

    isValidCardNumber(cardNumber) {
        // Basic Luhn algorithm implementation
        let sum = 0;
        let isEven = false;
        
        for (let i = cardNumber.length - 1; i >= 0; i--) {
            let digit = parseInt(cardNumber.charAt(i));
            
            if (isEven) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }
            
            sum += digit;
            isEven = !isEven;
        }
        
        return sum % 10 === 0 && cardNumber.length >= 13;
    }

    isValidExpiryDate(expiryDate) {
        const [month, year] = expiryDate.split('/');
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear() % 100;
        const currentMonth = currentDate.getMonth() + 1;

        const expMonth = parseInt(month);
        const expYear = parseInt(year);

        if (expMonth < 1 || expMonth > 12) return false;
        if (expYear < currentYear) return false;
        if (expYear === currentYear && expMonth < currentMonth) return false;

        return true;
    }

    isValidMobileNumber(number, provider) {
        // Remove all non-digit characters
        const cleanNumber = number.replace(/\D/g, '');
        
        switch (provider) {
            case 'mpesa':
                // Kenya M-Pesa numbers start with 254 or 0
                return /^(254|0)?[17]\d{8}$/.test(cleanNumber);
            case 'mtn':
                // MTN numbers vary by country
                return cleanNumber.length >= 9 && cleanNumber.length <= 15;
            case 'airtel':
                // Airtel numbers vary by country
                return cleanNumber.length >= 9 && cleanNumber.length <= 15;
            default:
                return cleanNumber.length >= 9;
        }
    }

    populateReview() {
        // Populate shipping information
        const shippingData = new FormData(document.getElementById('checkoutForm'));
        const shippingHtml = `
            <p><strong>${shippingData.get('firstName')} ${shippingData.get('lastName')}</strong></p>
            <p>${shippingData.get('address')}</p>
            ${shippingData.get('apartment') ? `<p>${shippingData.get('apartment')}</p>` : ''}
            <p>${shippingData.get('city')}, ${shippingData.get('state')} ${shippingData.get('zipCode')}</p>
            <p>${shippingData.get('country')}</p>
            <p>Phone: ${shippingData.get('phone')}</p>
            <p>Email: ${shippingData.get('email')}</p>
        `;
        document.getElementById('reviewShipping').innerHTML = shippingHtml;

        // Populate payment information
        const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
        let paymentHtml = '';

        switch (paymentMethod) {
            case 'card':
                const cardNumber = document.getElementById('cardNumber').value;
                const maskedCard = '**** **** **** ' + cardNumber.slice(-4);
                paymentHtml = `<p>Credit/Debit Card</p><p>${maskedCard}</p>`;
                break;
            case 'mobile':
                const provider = document.getElementById('mobileProvider').value;
                const mobileNumber = document.getElementById('mobileNumber').value;
                paymentHtml = `<p>Mobile Money - ${provider.toUpperCase()}</p><p>${mobileNumber}</p>`;
                break;
            case 'paypal':
                paymentHtml = '<p>PayPal</p>';
                break;
            case 'wallet':
                paymentHtml = '<p>Digital Wallet</p>';
                break;
        }
        document.getElementById('reviewPayment').innerHTML = paymentHtml;

        // Populate order items (already loaded)
        this.loadCartItems();
    }

    async processOrder() {
        const submitBtn = document.querySelector('.place-order-btn');
        const originalText = submitBtn.innerHTML;
        
        try {
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing Order...';
            submitBtn.disabled = true;

            // Collect all order data
            const formData = new FormData(document.getElementById('checkoutForm'));
            const orderData = {
                customer: {
                    firstName: formData.get('firstName'),
                    lastName: formData.get('lastName'),
                    email: formData.get('email'),
                    phone: formData.get('phone')
                },
                shipping: {
                    address: formData.get('address'),
                    apartment: formData.get('apartment'),
                    city: formData.get('city'),
                    state: formData.get('state'),
                    zipCode: formData.get('zipCode'),
                    country: formData.get('country'),
                    method: formData.get('shipping')
                },
                payment: {
                    method: formData.get('paymentMethod')
                },
                items: this.cartItems,
                pricing: this.orderData.pricing,
                orderId: 'M5-' + Date.now()
            };

            // Process payment based on method
            const paymentResult = await this.processPayment(orderData);
            
            if (paymentResult.success) {
                // Clear cart
                localStorage.removeItem('magna5rrr_cart');
                
                // Redirect to success page
                window.location.href = `order-success.html?order=${orderData.orderId}`;
            } else {
                throw new Error(paymentResult.message || 'Payment failed');
            }

        } catch (error) {
            this.showError('Order processing failed: ' + error.message);
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    async processPayment(orderData) {
        const paymentMethod = orderData.payment.method;
        
        switch (paymentMethod) {
            case 'card':
                return await this.processCardPayment(orderData);
            case 'mobile':
                return await this.processMobilePayment(orderData);
            case 'paypal':
                return await this.processPayPalPayment(orderData);
            case 'wallet':
                return await this.processWalletPayment(orderData);
            default:
                throw new Error('Invalid payment method');
        }
    }

    async processCardPayment(orderData) {
        // Simulate card payment processing
        return new Promise((resolve) => {
            setTimeout(() => {
                // Simulate 95% success rate
                const success = Math.random() > 0.05;
                resolve({
                    success: success,
                    transactionId: 'card_' + Date.now(),
                    message: success ? 'Payment successful' : 'Card declined'
                });
            }, 2000);
        });
    }

    async processMobilePayment(orderData) {
        const formData = new FormData(document.getElementById('checkoutForm'));
        const provider = formData.get('mobileProvider');
        const mobileNumber = formData.get('mobileNumber');

        // Simulate mobile money payment
        return new Promise((resolve) => {
            setTimeout(() => {
                // Show mobile money prompt simulation
                this.showMobileMoneyPrompt(provider, mobileNumber, orderData.pricing.total);
                
                // Simulate payment confirmation after 5 seconds
                setTimeout(() => {
                    const success = Math.random() > 0.1; // 90% success rate
                    resolve({
                        success: success,
                        transactionId: `${provider}_${Date.now()}`,
                        message: success ? 'Payment confirmed' : 'Payment cancelled or failed'
                    });
                }, 5000);
            }, 1000);
        });
    }

    async processPayPalPayment(orderData) {
        // Simulate PayPal redirect
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    transactionId: 'paypal_' + Date.now(),
                    message: 'PayPal payment successful'
                });
            }, 3000);
        });
    }

    async processWalletPayment(orderData) {
        // Simulate digital wallet payment
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    transactionId: 'wallet_' + Date.now(),
                    message: 'Digital wallet payment successful'
                });
            }, 1500);
        });
    }

    showMobileMoneyPrompt(provider, number, amount) {
        const modal = document.createElement('div');
        modal.className = 'mobile-money-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Mobile Money Payment</h3>
                    <div class="provider-logo">${provider.toUpperCase()}</div>
                </div>
                <div class="modal-body">
                    <div class="payment-details">
                        <p><strong>Amount:</strong> $${amount.toFixed(2)}</p>
                        <p><strong>Number:</strong> ${number}</p>
                        <p><strong>Merchant:</strong> Magna5RRR</p>
                    </div>
                    <div class="payment-status">
                        <div class="spinner"></div>
                        <p>Please check your phone and enter your PIN to complete the payment</p>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Remove modal after payment processing
        setTimeout(() => {
            modal.remove();
        }, 6000);
    }

    // Utility functions for form formatting
    formatCardNumber(e) {
        let value = e.target.value.replace(/\s/g, '');
        let formattedValue = value.replace(/(.{4})/g, '$1 ').trim();
        e.target.value = formattedValue;
    }

    formatExpiryDate(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 2) {
            value = value.substring(0, 2) + '/' + value.substring(2, 4);
        }
        e.target.value = value;
    }

    formatCVV(e) {
        e.target.value = e.target.value.replace(/\D/g, '');
    }

    formatMobileNumber(e) {
        let value = e.target.value.replace(/\D/g, '');
        // Add country code formatting based on provider
        e.target.value = value;
    }

    handleCountryChange(country) {
        // Update shipping costs and available payment methods based on country
        this.calculateTotals();
    }

    applyPromoCode() {
        const promoCode = document.getElementById('promoCode').value.trim().toUpperCase();
        const promoCodes = {
            'SAVE10': { discount: 0.10, message: '10% discount applied!' },
            'WELCOME20': { discount: 0.20, message: '20% welcome discount applied!' },
            'MAGNA5': { discount: 0.15, message: '15% Magna5RRR discount applied!' }
        };

        if (promoCodes[promoCode]) {
            // Apply discount and recalculate
            this.showSuccess(promoCodes[promoCode].message);
            this.calculateTotals();
        } else {
            this.showError('Invalid promo code');
        }
    }

    setupFormValidation() {
        // Add real-time validation for all form fields
        const inputs = document.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
    }

    validateField(field) {
        // Individual field validation logic
        return true;
    }

    showFieldError(field, message) {
        this.clearFieldError(field);
        field.classList.add('error');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        field.parentNode.appendChild(errorElement);
    }

    clearFieldError(field) {
        field.classList.remove('error');
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `checkout-notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize checkout system
document.addEventListener('DOMContentLoaded', () => {
    new CheckoutSystem();
});

// Add notification and modal styles
const style = document.createElement('style');
style.textContent = `
    .checkout-notification {
        position: fixed;
        top: 2rem;
        right: 2rem;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
    }

    .checkout-notification.success {
        background: var(--primary-50);
        border: 1px solid var(--primary-200);
        color: var(--primary-800);
    }

    .checkout-notification.error {
        background: var(--accent-50);
        border: 1px solid var(--accent-200);
        color: var(--accent-800);
    }

    .mobile-money-modal {
        position: fixed;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .modal-content {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        max-width: 400px;
        width: 90%;
        text-align: center;
    }

    .provider-logo {
        padding: 0.5rem 1rem;
        background: var(--primary-100);
        border-radius: 0.5rem;
        font-weight: bold;
        color: var(--primary-700);
        display: inline-block;
        margin-top: 0.5rem;
    }

    .payment-details {
        margin: 1.5rem 0;
        padding: 1rem;
        background: var(--neutral-50);
        border-radius: 0.5rem;
    }

    .spinner {
        width: 2rem;
        height: 2rem;
        border: 3px solid var(--neutral-200);
        border-top: 3px solid var(--primary-500);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 1rem auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .field-error {
        color: var(--accent-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-group input.error,
    .form-group select.error {
        border-color: var(--accent-500);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
`;
document.head.appendChild(style);
